declare -gx PKG_CONFIG_PATH_FOR_TARGET=/nix/store/wcki8li0sdcx3gks1n44fjp1qqy1nm1s-ffmpeg-6.1.1-dev/lib/pkgconfig
declare -gx REPLIT_SUBCLUSTER=paid
declare -gx REPLIT_RIPPKGS_INDICES=/nix/store/l5gcmdp908sji4wchfp8csflhjcgnmm3-rippkgs-indices
declare -gx USER=runner
declare -gx DISPLAY=:0
declare -gx GI_TYPELIB_PATH=''
declare -gx LOCALE_ARCHIVE=/usr/lib/locale/locale-archive
declare -gx REPLIT_DOMAINS=9238c0d7-abca-4cba-b07a-bef215918bc7-00-87acl2mxr0lr.riker.replit.dev
declare -gx XDG_CACHE_HOME=/home/<USER>/workspace/.cache
declare -gx REPL_LANGUAGE=nix
declare -gx REPL_HOME=/home/<USER>/workspace
declare -gx HOME=/home/<USER>
declare -gx LIBGL_DRIVERS_PATH=/nix/store/1z62rda9iqnxi4ryvgmyvfaj979hgk7s-mesa-24.2.8-drivers/lib/dri
declare -gx XDG_CONFIG_HOME=/home/<USER>/workspace/.config
declare -gx NIX_CFLAGS_COMPILE='-isystem /nix/store/wcki8li0sdcx3gks1n44fjp1qqy1nm1s-ffmpeg-6.1.1-dev/include'
declare -gx NIXPKGS_ALLOW_UNFREE=1
declare -gx REPLIT_BASHRC=/nix/store/jww70qbp1jafzdjawqy5sj0hpsp9xc03-replit-bashrc/bashrc
declare -gx REPL_ID=9238c0d7-abca-4cba-b07a-bef215918bc7
declare -gx REPLIT_PID1_FLAG_NIXMODULES_BEFORE_REPLIT_NIX=1
declare -gx REPL_SLUG=workspace
declare -gx NIX_PS1='\[\033[01;34m\]\w\[\033[00m\]\$ '
declare -gx npm_config_prefix=/home/<USER>/workspace/.config/npm/node_global
declare -gx NIX_PROFILES='/nix/var/nix/profiles/default /home/<USER>/.nix-profile'
declare -gx NIX_PATH=nixpkgs=/home/<USER>/.nix-defexpr/channels/nixpkgs-stable-24_05:/home/<USER>/.nix-defexpr/channels
declare -gx LANG=en_US.UTF-8
declare -gx REPLIT_DEV_DOMAIN=9238c0d7-abca-4cba-b07a-bef215918bc7-00-87acl2mxr0lr.riker.replit.dev
declare -gx __EGL_VENDOR_LIBRARY_FILENAMES=/nix/store/1z62rda9iqnxi4ryvgmyvfaj979hgk7s-mesa-24.2.8-drivers/share/glvnd/egl_vendor.d/50_mesa.json
declare -gx XDG_DATA_DIRS=/nix/store/8c6k4np7z9sdfx390qw3g1w2q3awmgz1-ffmpeg-6.1.1-doc/share:/nix/store/71hcrwn1g6ncki859v14jq18i8fx84sk-ffmpeg-6.1.1-data/share:/nix/store/jxv18sbi5vmhi0nxdn8nl8jnz6nd4djg-ffmpeg-6.1.1-man/share:/nix/store/z9q14j5s12vcm1xdk4kgg7mdh57k6hw4-replit-runtime-path/share
declare -gx REPL_PUBKEYS='{"crosis-ci":"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=","crosis-ci:1":"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=","crosis-ci:latest":"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=","prod":"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=","prod:1":"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=","prod:2":"8uGN+vfszlnV93/HCSHlVLG0xddMlPkir1Ni4JKT4+w=","prod:3":"9+MCOSHQSQlcodXoot8dC8NLhc862nLkx1/VMsbY2h8=","prod:4":"8uGN+vfszlnV93/HCSHlVLG0xddMlPkir1Ni4JKT4+w=","prod:5":"9+MCOSHQSQlcodXoot8dC8NLhc862nLkx1/VMsbY2h8=","prod:latest":"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=","vault-goval-token":"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E=","vault-goval-token:1":"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E=","vault-goval-token:latest":"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E="}'
declare -gx XDG_DATA_HOME=/home/<USER>/workspace/.local/share
declare -gx COLORTERM=truecolor
declare -gx REPLIT_CLI=/nix/store/v7wxan30g66j50k70bchmviqq01xjlr6-pid1-0.0.1/bin/replit
declare -gx REPL_OWNER_ID=36523258
declare -gx PROMPT_DIRTRIM=2
declare -gx REPLIT_LD_AUDIT=/nix/store/n5x1kgbz8zjh63ymsijbislyi1n1hir6-replit_rtld_loader-1/rtld_loader.so
declare -gx REPL_OWNER=clynch51
read -r _new_path <<< "/nix/store/3zc5jbvqzrn8zmva4fx5p0nh4yy03wk4-ffmpeg-6.1.1-bin/bin:/nix/store/0z5iwcvalafm3j2c5pfhllsfbxrbyzf4-postgresql-16.5/bin:/nix/store/r2wrscmjzn4f2f7wk7q2ms2h96mjwzv7-npx/bin:/home/<USER>/workspace/.config/npm/node_global/bin:/home/<USER>/workspace/node_modules/.bin:/nix/store/rrz8cqhldyl17bbs60g7d8vbaadkxc40-nodejs-20.18.1-wrapped/bin:/nix/store/5q4dz85wgqhifng1fk2xy85pslkmiqvs-bun-1.2.12/bin:/nix/store/z8s3r4vwf4r26g2d7shnw5lva6ihim8f-pnpm-9.15.0/bin:/nix/store/jcgdksj946l5l42c2y9ks2l4g6n74h3f-yarn-1.22.22/bin:/nix/store/2s17mrby0ph00z22rkabfs9vzpzx1r70-prettier-3.3.3/bin:/nix/store/hz8k9vcn26cbwf9i9qpxmhw1ryfyjj3i-pid1/bin:/nix/store/z9q14j5s12vcm1xdk4kgg7mdh57k6hw4-replit-runtime-path/bin:/home/<USER>/.nix-profile/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
#PATH=/nix/store/3zc5jbvqzrn8zmva4fx5p0nh4yy03wk4-ffmpeg-6.1.1-bin/bin:/nix/store/0z5iwcvalafm3j2c5pfhllsfbxrbyzf4-postgresql-16.5/bin:/nix/store/r2wrscmjzn4f2f7wk7q2ms2h96mjwzv7-npx/bin:/home/<USER>/workspace/.config/npm/node_global/bin:/home/<USER>/workspace/node_modules/.bin:/nix/store/rrz8cqhldyl17bbs60g7d8vbaadkxc40-nodejs-20.18.1-wrapped/bin:/nix/store/5q4dz85wgqhifng1fk2xy85pslkmiqvs-bun-1.2.12/bin:/nix/store/z8s3r4vwf4r26g2d7shnw5lva6ihim8f-pnpm-9.15.0/bin:/nix/store/jcgdksj946l5l42c2y9ks2l4g6n74h3f-yarn-1.22.22/bin:/nix/store/2s17mrby0ph00z22rkabfs9vzpzx1r70-prettier-3.3.3/bin:/nix/store/hz8k9vcn26cbwf9i9qpxmhw1ryfyjj3i-pid1/bin:/nix/store/z9q14j5s12vcm1xdk4kgg7mdh57k6hw4-replit-runtime-path/bin:/home/<USER>/.nix-profile/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
if [ -e "/run/replit/env/last" ]; then read -r _last_path < <(\grep '^#PATH=' /run/replit/env/last | cut -f 2 -d =); fi
_user_components="$(\tr : $'\n' <<< "${PATH:-}" |\grep -xv -f <(\tr : $'\n' <<< "${_last_path}") |\tr $'\n' :)"
declare -gx PATH="${_user_components}${_new_path}"
declare -gx GIT_EDITOR=replit-git-editor
declare -gx CFLAGS='-isystem /nix/store/wcki8li0sdcx3gks1n44fjp1qqy1nm1s-ffmpeg-6.1.1-dev/include'
declare -gx REPLIT_PID1_FLAG_REPLIT_RTLD_LOADER=1
declare -gx REPLIT_NIX_CHANNEL=stable-24_05
declare -gx REPLIT_ENVIRONMENT=production
declare -gx REPLIT_PID1_VERSION=0.0.0-555c024
declare -gx DOCKER_CONFIG=/home/<USER>/workspace/.config/docker
declare -gx REPLIT_RTLD_LOADER=1
declare -gx HOSTNAME=f6454af6fc90
declare -gx LDFLAGS='-L/nix/store/0qs6lqxic761r65ffdv6jqn6yrsjsqn0-ffmpeg-6.1.1-lib/lib -L/nix/store/wcki8li0sdcx3gks1n44fjp1qqy1nm1s-ffmpeg-6.1.1-dev/lib'
declare -gx PKG_CONFIG_PATH=/nix/store/wcki8li0sdcx3gks1n44fjp1qqy1nm1s-ffmpeg-6.1.1-dev/lib/pkgconfig
declare -gx REPLIT_CLUSTER=riker
declare -gx REPL_IMAGE=gcr.io/marine-cycle-160323/nix:bf8590a3e2f0a8b70b7ca175eeed9074dffbfca9
declare -gx GIT_ASKPASS=replit-git-askpass
declare -gx NIX_LDFLAGS='-L/nix/store/0qs6lqxic761r65ffdv6jqn6yrsjsqn0-ffmpeg-6.1.1-lib/lib -L/nix/store/wcki8li0sdcx3gks1n44fjp1qqy1nm1s-ffmpeg-6.1.1-dev/lib'
declare -gx REPLIT_LD_LIBRARY_PATH=/nix/store/0qs6lqxic761r65ffdv6jqn6yrsjsqn0-ffmpeg-6.1.1-lib/lib:/nix/store/wcki8li0sdcx3gks1n44fjp1qqy1nm1s-ffmpeg-6.1.1-dev/lib
