{"channel": "stable-24_05", "channel_nix_path": "/nix/store/r9dxa39mzcwfm6qp398j3wkms3vwiqcd-nixpkgs-stable-24_05-24.05.tar.gz/nixpkgs-stable-24_05", "env": {"CFLAGS": "-isystem /nix/store/wcki8li0sdcx3gks1n44fjp1qqy1nm1s-ffmpeg-6.1.1-dev/include", "GI_TYPELIB_PATH": "", "LDFLAGS": "-L/nix/store/0qs6lqxic761r65ffdv6jqn6yrsjsqn0-ffmpeg-6.1.1-lib/lib -L/nix/store/wcki8li0sdcx3gks1n44fjp1qqy1nm1s-ffmpeg-6.1.1-dev/lib", "NIX_CFLAGS_COMPILE": "-isystem /nix/store/wcki8li0sdcx3gks1n44fjp1qqy1nm1s-ffmpeg-6.1.1-dev/include", "NIX_LDFLAGS": "-L/nix/store/0qs6lqxic761r65ffdv6jqn6yrsjsqn0-ffmpeg-6.1.1-lib/lib -L/nix/store/wcki8li0sdcx3gks1n44fjp1qqy1nm1s-ffmpeg-6.1.1-dev/lib", "PATH": "/nix/store/3zc5jbvqzrn8zmva4fx5p0nh4yy03wk4-ffmpeg-6.1.1-bin/bin", "PKG_CONFIG_PATH": "/nix/store/wcki8li0sdcx3gks1n44fjp1qqy1nm1s-ffmpeg-6.1.1-dev/lib/pkgconfig", "PKG_CONFIG_PATH_FOR_TARGET": "/nix/store/wcki8li0sdcx3gks1n44fjp1qqy1nm1s-ffmpeg-6.1.1-dev/lib/pkgconfig", "REPLIT_LD_LIBRARY_PATH": "/nix/store/0qs6lqxic761r65ffdv6jqn6yrsjsqn0-ffmpeg-6.1.1-lib/lib:/nix/store/wcki8li0sdcx3gks1n44fjp1qqy1nm1s-ffmpeg-6.1.1-dev/lib", "XDG_DATA_DIRS": "/nix/store/8c6k4np7z9sdfx390qw3g1w2q3awmgz1-ffmpeg-6.1.1-doc/share:/nix/store/71hcrwn1g6ncki859v14jq18i8fx84sk-ffmpeg-6.1.1-data/share:/nix/store/jxv18sbi5vmhi0nxdn8nl8jnz6nd4djg-ffmpeg-6.1.1-man/share"}, "packages": ["ffmpeg"]}