#!/usr/bin/env node

import { WebSocket } from 'ws';

const WEBSOCKET_URL = 'ws://localhost:5000/ws';

console.log('🎯 Testing Server-Side VAD Configuration...');
console.log('📡 Connecting to:', WEBSOCKET_URL);

const ws = new WebSocket(WEBSOCKET_URL);

let messageCount = 0;
const receivedMessages = [];

ws.on('open', () => {
  console.log('✅ WebSocket connected');
  
  // Send start message with new model
  const startMessage = {
    type: "start",
    userId: "vad-test",
    clientId: "vad-test",
    useRealtimeAPI: true,
    mode: "realtime",
    behavior: {
      model: "gpt-4o-realtime-preview-2025-06-03",
      temperature: 0.7,
      voice: {
        voice: "shimmer",
        speed: 1.0
      }
    },
    instructions: "You are <PERSON>, an empathetic AI therapeutic assistant. Keep responses brief for testing."
  };
  
  console.log('📤 Starting session with new model and server-side VAD...');
  ws.send(JSON.stringify(startMessage));
});

ws.on('message', (data) => {
  messageCount++;
  try {
    const message = JSON.parse(data.toString());
    console.log(`📥 Message ${messageCount}: ${message.type}`);
    receivedMessages.push(message.type);
    
    // Handle specific message types
    switch (message.type) {
      case 'session.created':
        console.log('✅ Session created with session ID:', message.session?.id);
        break;
        
      case 'session.updated':
        console.log('✅ Session updated - VAD configuration applied');
        if (message.session?.turn_detection) {
          console.log('🎯 Turn detection config:', JSON.stringify(message.session.turn_detection, null, 2));
        } else {
          console.log('⚠️ Turn detection is null - using manual mode');
        }
        break;
        
      case 'ready':
        console.log('✅ Session ready! Testing continuous audio streaming...');
        
        // Start streaming audio continuously after a short delay
        setTimeout(() => {
          console.log('🎤 Starting continuous audio stream...');
          streamContinuousAudio();
        }, 1000);
        break;
        
      case 'input_audio_buffer.speech_started':
        console.log('🎤 SERVER VAD: Speech started detected by OpenAI!');
        break;
        
      case 'input_audio_buffer.speech_stopped':
        console.log('🔇 SERVER VAD: Speech stopped detected by OpenAI!');
        break;
        
      case 'conversation.item.input_audio_transcription.completed':
        console.log(`🎯 User speech transcribed: "${message.transcript}"`);
        break;
        
      case 'response.audio_transcript.done':
        console.log(`🤖 AI response: "${message.transcript}"`);
        break;
        
      case 'response.done':
        console.log('✅ AI response completed');
        
        // End test after first response
        setTimeout(() => {
          console.log('📤 Ending test...');
          ws.send(JSON.stringify({
            type: 'conversationEnded'
          }));
        }, 1000);
        break;
        
      case 'end_ack':
        console.log('✅ Test completed successfully');
        
        setTimeout(() => {
          console.log('\n=== SERVER VAD TEST SUMMARY ===');
          console.log('Received messages:', receivedMessages);
          console.log('✅ Server-side VAD test completed');
          ws.close();
        }, 500);
        break;
        
      case 'error':
        console.error('❌ Error:', message.message);
        if (message.error) {
          console.error('Error details:', message.error);
        }
        break;
    }
  } catch (error) {
    console.error('❌ Error parsing message:', error);
  }
});

// Function to stream continuous audio (simulating real microphone input)
function streamContinuousAudio() {
  let chunkCount = 0;
  const maxChunks = 100; // Stream for about 4 seconds
  
  const streamInterval = setInterval(() => {
    chunkCount++;
    
    // Generate realistic speech-like audio
    const sampleRate = 24000;
    const chunkDurationMs = 40; // 40ms chunks
    const samples = Math.floor(sampleRate * chunkDurationMs / 1000);
    const audioBuffer = new Int16Array(samples);
    
    // Create speech-like patterns with varying intensity
    const speechIntensity = chunkCount > 20 && chunkCount < 60 ? 0.4 : 0.05; // Speech in middle
    
    for (let i = 0; i < samples; i++) {
      const time = (chunkCount * chunkDurationMs / 1000) + (i / sampleRate);
      const frequency = 200 + Math.sin(time * 10) * 50;
      const amplitude = speechIntensity * (0.5 + 0.5 * Math.sin(time * 3));
      
      const signal = Math.sin(2 * Math.PI * frequency * time) * amplitude;
      const noise = (Math.random() - 0.5) * 0.1 * amplitude;
      
      audioBuffer[i] = Math.floor((signal + noise) * 16000);
    }
    
    // Convert to base64
    const buffer = Buffer.from(audioBuffer.buffer);
    const base64Audio = buffer.toString('base64');
    
    // Send audio chunk
    ws.send(JSON.stringify({
      type: 'input_audio_buffer.append',
      audio: base64Audio
    }));
    
    // Log progress
    if (chunkCount % 25 === 0) {
      console.log(`📤 Streamed ${chunkCount} audio chunks (${speechIntensity > 0.1 ? 'SPEECH' : 'silence'})`);
    }
    
    // Stop streaming after max chunks
    if (chunkCount >= maxChunks) {
      clearInterval(streamInterval);
      console.log('🔇 Audio streaming completed');
    }
  }, 40); // Send chunk every 40ms
}

ws.on('close', (code, reason) => {
  console.log(`🔌 Connection closed: ${code} ${reason.toString()}`);
});

ws.on('error', (error) => {
  console.error('❌ WebSocket error:', error);
});

// Timeout to prevent hanging
setTimeout(() => {
  console.log('⏰ Test timeout');
  ws.close();
  process.exit(0);
}, 30000); // 30 second timeout
