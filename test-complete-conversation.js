#!/usr/bin/env node

import { WebSocket } from 'ws';

const WEBSOCKET_URL = 'ws://localhost:5000/ws';

console.log('🗣️ Testing Complete Real-time Conversation with Server VAD...');
console.log('📡 Connecting to:', WEBSOCKET_URL);

const ws = new WebSocket(WEBSOCKET_URL);

let messageCount = 0;
let conversationStep = 0;
let speechStarted = false;
let speechEnded = false;
let responseReceived = false;

ws.on('open', () => {
  console.log('✅ WebSocket connected');
  
  const startMessage = {
    type: "start",
    userId: "conversation-test",
    clientId: "conversation-test",
    useRealtimeAPI: true,
    mode: "realtime",
    behavior: {
      model: "gpt-4o-realtime-preview-2025-06-03",
      temperature: 0.7,
      voice: {
        voice: "shimmer",
        speed: 1.0
      }
    },
    instructions: "You are <PERSON>, an empathetic AI therapeutic assistant. Keep responses very brief (1-2 sentences) for this test conversation."
  };
  
  console.log('📤 Starting conversation with server-side VAD...');
  ws.send(JSON.stringify(startMessage));
});

ws.on('message', (data) => {
  messageCount++;
  try {
    const message = JSON.parse(data.toString());
    
    // Only log important events
    if (['ready', 'input_audio_buffer.speech_started', 'input_audio_buffer.speech_stopped', 
         'conversation.item.input_audio_transcription.completed', 'response.audio_transcript.done', 
         'response.done', 'error'].includes(message.type)) {
      console.log(`📥 ${message.type}`);
    }
    
    switch (message.type) {
      case 'ready':
        console.log('✅ Session ready! Waiting for AI greeting...');
        break;
        
      case 'response.done':
        if (conversationStep === 0) {
          console.log('✅ AI greeting completed. Starting user speech simulation...');
          conversationStep = 1;
          
          // Start speaking after AI finishes greeting
          setTimeout(() => {
            startSpeechSimulation();
          }, 1000);
        } else {
          console.log('✅ AI response to user completed!');
          responseReceived = true;
          
          // End conversation after successful response
          setTimeout(() => {
            console.log('📤 Ending conversation...');
            ws.send(JSON.stringify({
              type: 'conversationEnded'
            }));
          }, 1000);
        }
        break;
        
      case 'input_audio_buffer.speech_started':
        console.log('🎤 SERVER VAD: Speech detection started!');
        speechStarted = true;
        break;
        
      case 'input_audio_buffer.speech_stopped':
        console.log('🔇 SERVER VAD: Speech detection stopped!');
        speechEnded = true;
        break;
        
      case 'conversation.item.input_audio_transcription.completed':
        console.log(`🎯 User speech transcribed: "${message.transcript}"`);
        break;
        
      case 'response.audio_transcript.done':
        console.log(`🤖 AI response: "${message.transcript}"`);
        break;
        
      case 'end_ack':
        console.log('✅ Conversation ended successfully');
        
        setTimeout(() => {
          console.log('\n=== COMPLETE CONVERSATION TEST SUMMARY ===');
          console.log(`✅ Speech started detected: ${speechStarted}`);
          console.log(`✅ Speech stopped detected: ${speechEnded}`);
          console.log(`✅ AI response received: ${responseReceived}`);
          console.log(`📊 Total messages: ${messageCount}`);
          
          if (speechStarted && speechEnded && responseReceived) {
            console.log('🎉 COMPLETE CONVERSATION TEST PASSED!');
          } else {
            console.log('❌ Test incomplete - missing some events');
          }
          
          ws.close();
        }, 500);
        break;
        
      case 'error':
        console.error('❌ Error:', message.message);
        break;
    }
  } catch (error) {
    console.error('❌ Error parsing message:', error);
  }
});

// Function to simulate realistic speech with proper timing
function startSpeechSimulation() {
  console.log('🎤 Starting speech simulation...');
  
  let chunkCount = 0;
  const speechDurationMs = 3000; // 3 seconds of speech
  const chunkIntervalMs = 50; // 50ms chunks
  const totalChunks = speechDurationMs / chunkIntervalMs;
  
  const speechInterval = setInterval(() => {
    chunkCount++;
    
    // Generate speech-like audio with realistic patterns
    const sampleRate = 24000;
    const samples = Math.floor(sampleRate * chunkIntervalMs / 1000);
    const audioBuffer = new Int16Array(samples);
    
    // Create realistic speech intensity curve
    const progress = chunkCount / totalChunks;
    const speechIntensity = 0.6 * Math.sin(progress * Math.PI); // Bell curve intensity
    
    for (let i = 0; i < samples; i++) {
      const time = (chunkCount * chunkIntervalMs / 1000) + (i / sampleRate);
      
      // Multiple frequency components for speech-like sound
      const f1 = 200 + Math.sin(time * 8) * 50;  // Fundamental frequency
      const f2 = 800 + Math.sin(time * 12) * 100; // First formant
      const f3 = 2400 + Math.sin(time * 6) * 200; // Second formant
      
      const amplitude = speechIntensity * (0.7 + 0.3 * Math.sin(time * 15));
      
      // Combine frequencies with different amplitudes
      const signal = (
        Math.sin(2 * Math.PI * f1 * time) * 0.5 +
        Math.sin(2 * Math.PI * f2 * time) * 0.3 +
        Math.sin(2 * Math.PI * f3 * time) * 0.2
      ) * amplitude;
      
      // Add some noise for realism
      const noise = (Math.random() - 0.5) * 0.1 * amplitude;
      
      audioBuffer[i] = Math.floor((signal + noise) * 12000);
    }
    
    // Convert to base64 and send
    const buffer = Buffer.from(audioBuffer.buffer);
    const base64Audio = buffer.toString('base64');
    
    ws.send(JSON.stringify({
      type: 'input_audio_buffer.append',
      audio: base64Audio
    }));
    
    // Log progress every 20 chunks
    if (chunkCount % 20 === 0) {
      console.log(`📤 Speech progress: ${Math.round(progress * 100)}%`);
    }
    
    // Stop after total chunks
    if (chunkCount >= totalChunks) {
      clearInterval(speechInterval);
      console.log('🔇 Speech simulation completed - waiting for server VAD to detect end...');
    }
  }, chunkIntervalMs);
}

ws.on('close', (code, reason) => {
  console.log(`🔌 Connection closed: ${code} ${reason.toString()}`);
});

ws.on('error', (error) => {
  console.error('❌ WebSocket error:', error);
});

// Timeout to prevent hanging
setTimeout(() => {
  console.log('⏰ Test timeout - ending test');
  ws.close();
  process.exit(0);
}, 45000); // 45 second timeout
