*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[01:26:39] 




[01:26:39] Extension host agent started.
[01:26:39] Started initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
[01:26:39] Completed initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
[01:26:39] [<unknown>][646be9be][ExtensionHostConnection] New connection established.
[01:26:39] [<unknown>][33480e20][ManagementConnection] New connection established.
[01:26:39] [<unknown>][646be9be][ExtensionHostConnection] <368> Launched Extension Host Process.
[01:26:39] [network] #1: https://az764295.vo.msecnd.net/extensions/marketplace.json - error GET getaddrinfo ENOTFOUND az764295.vo.msecnd.net
[01:26:56] [<unknown>][33480e20][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[01:26:56] [<unknown>][646be9be][ExtensionHostConnection] <368> Extension Host Process exited with code: 0, signal: null.
Cancelling previous shutdown timeout
[01:26:56] Cancelling previous shutdown timeout
Last EH closed, waiting before shutting down
[01:26:56] Last EH closed, waiting before shutting down
[01:28:05] Extensions added from another source augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
Last EH closed, shutting down
[01:31:56] Last EH closed, shutting down
