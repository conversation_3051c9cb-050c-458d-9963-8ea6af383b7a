{"name": "debug-auto-launch", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "^1.5.0"}, "icon": "media/icon.png", "capabilities": {"virtualWorkspaces": false, "untrustedWorkspaces": {"supported": true}}, "activationEvents": ["onStartupFinished"], "main": "./dist/extension", "contributes": {"commands": [{"command": "extension.node-debug.toggleAutoAttach", "title": "%toggle.auto.attach%", "category": "Debug"}]}, "prettier": {"printWidth": 100, "trailingComma": "all", "singleQuote": true, "arrowParens": "avoid"}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}