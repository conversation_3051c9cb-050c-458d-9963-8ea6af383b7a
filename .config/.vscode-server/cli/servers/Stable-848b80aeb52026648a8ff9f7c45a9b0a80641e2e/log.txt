*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[01:27:18] 




[01:27:19] Extension host agent started.
[01:27:19] [<unknown>][03688584][ExtensionHostConnection] New connection established.
[01:27:19] [<unknown>][7551ebb9][ManagementConnection] New connection established.
[01:27:19] [<unknown>][03688584][ExtensionHostConnection] <653> Launched Extension Host Process.
[01:27:19] ComputeTargetPlatform: linux-x64
[01:27:22] ComputeTargetPlatform: linux-x64
[01:28:02] Getting Manifest... augment.vscode-augment
[01:28:02] Installing extension: augment.vscode-augment {
  isMachineScoped: false,
  installPreReleaseVersion: false,
  pinned: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'darwin-x64' },
  isApplicationScoped: false,
  profileLocation: _r {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  },
  productVersion: { version: '1.100.2', date: '2025-05-14T21:47:40.416Z' }
}
[01:28:04] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 1701ms.
[01:28:05] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.472.1: augment.vscode-augment
[01:28:05] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.472.1
[01:28:05] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
New EH opened, aborting shutdown
[01:32:19] New EH opened, aborting shutdown
