2025-06-05 01:27:19.080 [info] 




2025-06-05 01:27:19.080 [info] Extension host agent started.
2025-06-05 01:27:19.275 [info] [<unknown>][03688584][ExtensionHostConnection] New connection established.
2025-06-05 01:27:19.293 [info] [<unknown>][7551ebb9][ManagementConnection] New connection established.
2025-06-05 01:27:19.437 [info] [<unknown>][03688584][ExtensionHostConnection] <653> Launched Extension Host Process.
2025-06-05 01:27:19.687 [info] ComputeTargetPlatform: linux-x64
2025-06-05 01:27:22.205 [info] ComputeTargetPlatform: linux-x64
2025-06-05 01:28:02.315 [info] Getting Manifest... augment.vscode-augment
2025-06-05 01:28:02.466 [info] Installing extension: augment.vscode-augment {"isMachineScoped":false,"installPreReleaseVersion":false,"pinned":false,"donotVerifySignature":false,"context":{"clientTargetPlatform":"darwin-x64"},"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.100.2","date":"2025-05-14T21:47:40.416Z"}}
2025-06-05 01:28:04.761 [info] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 1701ms.
2025-06-05 01:28:05.737 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.472.1: augment.vscode-augment
2025-06-05 01:28:05.779 [info] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.472.1
2025-06-05 01:28:05.789 [info] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-06-05 01:32:19.075 [info] New EH opened, aborting shutdown
