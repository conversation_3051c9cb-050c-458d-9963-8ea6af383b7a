2025-06-05 01:28:06.639 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-05 01:28:06.639 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-05 01:28:06.639 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5}
2025-06-05 01:28:06.701 [info] 'AugmentExtension' Retrieving model config
2025-06-05 01:28:06.948 [info] 'AugmentExtension' Retrieved model config
2025-06-05 01:28:06.948 [info] 'AugmentExtension' Returning model config
2025-06-05 01:28:07.055 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 24576
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
2025-06-05 01:28:07.056 [info] 'SyncingPermissionTracker' Initial syncing permission: undefined
2025-06-05 01:28:07.056 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-05 01:28:07.056 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-05 01:28:07.056 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace unknown: no permission information recorded
2025-06-05 01:28:07.056 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = unknown
2025-06-05 01:28:07.101 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-05 01:28:07.102 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-05 01:28:07.102 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-05 01:28:07.104 [info] 'ToolsModel' Loaded saved chat mode: CHAT
2025-06-05 01:28:07.109 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-06-05 01:28:07.124 [info] 'WorkspaceManager' Beginning full qualification of source folder /home/<USER>/workspace
2025-06-05 01:28:07.737 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-06-05 01:28:08.964 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-05 01:28:08.965 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-05 01:28:08.965 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentWindow=false, remoteAgentId=undefined
2025-06-05 01:28:08.965 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-05 01:28:08.965 [info] 'TaskManager' Setting current root task UUID to 7010bd30-91d6-49a5-ada6-bb3fbbe4be9e
2025-06-05 01:28:08.965 [info] 'TaskManager' Setting current root task UUID to 7010bd30-91d6-49a5-ada6-bb3fbbe4be9e
2025-06-05 01:28:09.148 [info] 'TaskManager' Setting current root task UUID to c43012f7-2cc1-494d-ab34-d6d0f88cc413
2025-06-05 01:28:09.148 [info] 'TaskManager' Setting current root task UUID to c43012f7-2cc1-494d-ab34-d6d0f88cc413
2025-06-05 01:28:09.149 [info] 'TaskManager' Setting current root task UUID to d4df710d-f181-4b13-9885-ffb8da904309
2025-06-05 01:28:09.149 [info] 'TaskManager' Setting current root task UUID to d4df710d-f181-4b13-9885-ffb8da904309
2025-06-05 01:28:11.403 [info] 'WorkspaceManager' Finished full qualification of source folder /home/<USER>/workspace: trackable files: 1597, uploaded fraction: 0.046, is repo: true
2025-06-05 01:28:11.404 [info] 'WorkspaceManager' Requesting syncing permission because source folder has less than 90% of files uploaded
2025-06-05 01:28:11.407 [info] 'AwaitingSyncingPermissionApp' Registering AwaitingSyncingPermissionApp
2025-06-05 01:28:13.977 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-71bc09f2-c313-492b-b956-e11643c46bfa.json'
2025-06-05 01:28:15.712 [info] 'AwaitingSyncingPermissionApp' User granted syncing permission
2025-06-05 01:28:15.712 [info] 'WorkspaceManager' Enabling syncing for all trackable source folders
2025-06-05 01:28:15.712 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/5/2025, 1:28:15 AM
2025-06-05 01:28:15.821 [info] 'WorkspaceManager[workspace]' Start tracking
2025-06-05 01:28:15.823 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-06-05 01:28:15.823 [info] 'OpenFileManager' Opened source folder 100
2025-06-05 01:28:15.824 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-05 01:28:15.824 [info] 'MtimeCache[workspace]' no blob name cache found at /home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json (probably new source folder); error = ENOENT: no such file or directory, open '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json'
2025-06-05 01:28:15.991 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-05 01:28:15.991 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-05 01:28:16.321 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-05 01:28:16.321 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentWindow=false, remoteAgentId=undefined
2025-06-05 01:28:16.321 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-05 01:28:16.321 [info] 'TaskManager' Setting current root task UUID to e63218cb-f243-4533-a019-7f5c04682545
2025-06-05 01:28:16.321 [info] 'TaskManager' Setting current root task UUID to e63218cb-f243-4533-a019-7f5c04682545
2025-06-05 01:28:16.461 [info] 'TaskManager' Setting current root task UUID to f696643d-f727-4e88-97b6-598700341fc9
2025-06-05 01:28:16.461 [info] 'TaskManager' Setting current root task UUID to f696643d-f727-4e88-97b6-598700341fc9
2025-06-05 01:28:24.176 [info] 'ToolsModel' Saved chat mode: AGENT
2025-06-05 01:28:24.176 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-05 01:28:24.176 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-05 01:28:24.473 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-05 01:28:24.473 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-05 01:28:51.283 [error] 'AugmentExtension' API request 1e4c4d1d-7a17-428d-9376-19b9e5cac14a to https://i0.api.augmentcode.com/batch-upload failed: This operation was aborted
2025-06-05 01:28:51.511 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-05 01:28:52.169 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-05 01:29:01.169 [info] 'ToolsModel' Saved chat mode: CHAT
2025-06-05 01:29:01.169 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-06-05 01:29:01.169 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-05 01:29:01.186 [info] 'ToolsModel' Saved chat mode: AGENT
2025-06-05 01:29:01.186 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-05 01:29:01.186 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-05 01:29:01.225 [info] 'TaskManager' Setting current root task UUID to a8952510-18f3-457e-8b74-************
2025-06-05 01:29:01.226 [info] 'TaskManager' Setting current root task UUID to a8952510-18f3-457e-8b74-************
2025-06-05 01:29:01.460 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-05 01:29:01.460 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-05 01:29:16.194 [info] 'WorkspaceManager[workspace]' Tracking enabled
2025-06-05 01:29:16.195 [info] 'WorkspaceManager[workspace]' Path metrics:
  - directories emitted: 428
  - files emitted: 1603
  - other paths emitted: 4
  - total paths emitted: 2035
  - timing stats:
    - readDir: 9 ms
    - filter: 61 ms
    - yield: 13 ms
    - total: 86 ms
2025-06-05 01:29:16.195 [info] 'WorkspaceManager[workspace]' File metrics:
  - paths accepted: 1318
  - paths not accessible: 0
  - not plain files: 0
  - large files: 24
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 0
  - mtime cache misses: 1318
  - probe batches: 21
  - blob names probed: 4590
  - files read: 2800
  - blobs uploaded: 1131
  - timing stats:
    - ingestPath: 11 ms
    - probe: 12108 ms
    - stat: 54 ms
    - read: 4057 ms
    - upload: 40497 ms
2025-06-05 01:29:16.195 [info] 'WorkspaceManager[workspace]' Startup metrics:
  - create SourceFolder: 2 ms
  - read MtimeCache: 1 ms
  - pre-populate PathMap: 0 ms
  - create PathFilter: 38 ms
  - create PathNotifier: 1 ms
  - enumerate paths: 125 ms
  - purge stale PathMap entries: 1 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 60201 ms
  - enable persist: 4 ms
  - total: 60373 ms
2025-06-05 01:29:16.195 [info] 'WorkspaceManager' Workspace startup complete in 69209 ms
2025-06-05 01:29:16.336 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1
2025-06-05 01:29:55.698 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-05 01:30:49.125 [error] 'AugmentExtension' API request c4846dc7-c548-4677-8400-f6d983fb6510 to https://i0.api.augmentcode.com/batch-upload failed: This operation was aborted
2025-06-05 01:30:49.391 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-05 01:30:49.816 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
