2025-06-05 01:27:22.988 [info] [main] Log level: Info
2025-06-05 01:27:22.988 [info] [main] Validating found git in: "git"
2025-06-05 01:27:22.988 [info] [main] Using git "2.47.2" from "git"
2025-06-05 01:27:22.988 [info] [Model][doInitialScan] Initial repository scan started
2025-06-05 01:27:22.988 [info] > git rev-parse --show-toplevel [52ms]
2025-06-05 01:27:22.988 [info] > git rev-parse --git-dir --git-common-dir [5ms]
2025-06-05 01:27:22.988 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-05 01:27:22.989 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-05 01:27:22.989 [info] > git config --get commit.template [5ms]
2025-06-05 01:27:22.989 [info] > git rev-parse --show-toplevel [3ms]
2025-06-05 01:27:22.999 [info] > git rev-parse --show-toplevel [7ms]
2025-06-05 01:27:22.999 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [12ms]
2025-06-05 01:27:23.012 [info] > git rev-parse --show-toplevel [6ms]
2025-06-05 01:27:23.032 [info] > git status -z -uall [12ms]
2025-06-05 01:27:23.033 [info] > git rev-parse --show-toplevel [17ms]
2025-06-05 01:27:23.036 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:27:23.067 [info] > git rev-parse --show-toplevel [31ms]
2025-06-05 01:27:23.106 [info] > git config --get commit.template [34ms]
2025-06-05 01:27:23.106 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [50ms]
2025-06-05 01:27:23.113 [info] > git rev-parse --show-toplevel [34ms]
2025-06-05 01:27:23.117 [info] > git config --get --local branch.main.vscode-merge-base [7ms]
2025-06-05 01:27:23.125 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-05 01:27:23.126 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [5ms]
2025-06-05 01:27:23.132 [info] > git merge-base refs/heads/main refs/remotes/origin/main [2ms]
2025-06-05 01:27:23.136 [info] > git rev-parse --show-toplevel [11ms]
2025-06-05 01:27:23.142 [info] > git diff --name-status -z --diff-filter=ADMR 40f156400ee62158de598c268b329e23343defd1...refs/remotes/origin/main [7ms]
2025-06-05 01:27:23.143 [info] > git rev-parse --show-toplevel [3ms]
2025-06-05 01:27:23.155 [info] > git status -z -uall [7ms]
2025-06-05 01:27:23.156 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-05 01:27:23.162 [info] > git rev-parse --show-toplevel [8ms]
2025-06-05 01:27:23.199 [info] > git rev-parse --show-toplevel [33ms]
2025-06-05 01:27:23.205 [info] > git rev-parse --show-toplevel [3ms]
2025-06-05 01:27:23.211 [info] > git rev-parse --show-toplevel [2ms]
2025-06-05 01:27:23.218 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-05 01:27:23.503 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-05 01:27:27.091 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-05 01:27:27.091 [info] > git config --get commit.template [10ms]
2025-06-05 01:27:27.096 [info] > git status -z -uall [2ms]
2025-06-05 01:27:27.098 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:27:43.250 [info] > git config --get commit.template [3ms]
2025-06-05 01:27:43.251 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:27:43.257 [info] > git status -z -uall [3ms]
2025-06-05 01:27:43.261 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:27:48.271 [info] > git config --get commit.template [3ms]
2025-06-05 01:27:48.271 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:27:48.277 [info] > git status -z -uall [3ms]
2025-06-05 01:27:48.278 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:27:53.289 [info] > git config --get commit.template [3ms]
2025-06-05 01:27:53.289 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:27:53.296 [info] > git status -z -uall [4ms]
2025-06-05 01:27:53.297 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:27:58.306 [info] > git config --get commit.template [2ms]
2025-06-05 01:27:58.306 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:27:58.313 [info] > git status -z -uall [4ms]
2025-06-05 01:27:58.314 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:28:03.324 [info] > git config --get commit.template [2ms]
2025-06-05 01:28:03.324 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:28:03.330 [info] > git status -z -uall [3ms]
2025-06-05 01:28:03.331 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:28:19.475 [info] > git config --get commit.template [15ms]
2025-06-05 01:28:19.477 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-05 01:28:19.496 [info] > git status -z -uall [7ms]
2025-06-05 01:28:19.497 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:28:24.513 [info] > git config --get commit.template [6ms]
2025-06-05 01:28:24.514 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:28:24.540 [info] > git status -z -uall [13ms]
2025-06-05 01:28:24.541 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-05 01:28:29.553 [info] > git config --get commit.template [1ms]
2025-06-05 01:28:29.561 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:28:29.576 [info] > git status -z -uall [5ms]
2025-06-05 01:28:29.576 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:28:34.589 [info] > git config --get commit.template [3ms]
2025-06-05 01:28:34.590 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:28:34.600 [info] > git status -z -uall [5ms]
2025-06-05 01:28:34.600 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:28:39.616 [info] > git config --get commit.template [5ms]
2025-06-05 01:28:39.617 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:28:39.627 [info] > git status -z -uall [5ms]
2025-06-05 01:28:39.628 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:28:44.643 [info] > git config --get commit.template [5ms]
2025-06-05 01:28:44.644 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:28:44.653 [info] > git status -z -uall [4ms]
2025-06-05 01:28:44.654 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:28:49.668 [info] > git config --get commit.template [5ms]
2025-06-05 01:28:49.669 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:28:49.679 [info] > git status -z -uall [6ms]
2025-06-05 01:28:49.679 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:28:54.730 [info] > git config --get commit.template [9ms]
2025-06-05 01:28:54.731 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:28:54.748 [info] > git status -z -uall [9ms]
2025-06-05 01:28:54.748 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:28:59.764 [info] > git config --get commit.template [6ms]
2025-06-05 01:28:59.765 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:28:59.783 [info] > git status -z -uall [10ms]
2025-06-05 01:28:59.785 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:29:04.809 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:29:04.809 [info] > git config --get commit.template [8ms]
2025-06-05 01:29:04.827 [info] > git status -z -uall [11ms]
2025-06-05 01:29:04.832 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-05 01:29:09.842 [info] > git config --get commit.template [0ms]
2025-06-05 01:29:09.858 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-05 01:29:09.860 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-05 01:29:09.876 [info] > git status -z -uall [8ms]
2025-06-05 01:29:09.987 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [112ms]
2025-06-05 01:29:18.183 [info] > git config --get commit.template [4ms]
2025-06-05 01:29:18.184 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:29:18.194 [info] > git status -z -uall [6ms]
2025-06-05 01:29:18.195 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:29:23.208 [info] > git config --get commit.template [4ms]
2025-06-05 01:29:23.210 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:29:23.220 [info] > git status -z -uall [6ms]
2025-06-05 01:29:23.221 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:29:28.241 [info] > git config --get commit.template [10ms]
2025-06-05 01:29:28.243 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:29:28.255 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:29:28.260 [info] > git status -z -uall [10ms]
2025-06-05 01:29:33.274 [info] > git config --get commit.template [5ms]
2025-06-05 01:29:33.275 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:29:33.284 [info] > git status -z -uall [5ms]
2025-06-05 01:29:33.285 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:29:38.293 [info] > git config --get commit.template [1ms]
2025-06-05 01:29:38.302 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:29:38.337 [info] > git status -z -uall [11ms]
2025-06-05 01:29:38.338 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:29:43.352 [info] > git config --get commit.template [6ms]
2025-06-05 01:29:43.353 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:29:43.362 [info] > git status -z -uall [4ms]
2025-06-05 01:29:43.362 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:29:48.378 [info] > git config --get commit.template [7ms]
2025-06-05 01:29:48.379 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:29:48.387 [info] > git status -z -uall [4ms]
2025-06-05 01:29:48.389 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:29:53.423 [info] > git config --get commit.template [1ms]
2025-06-05 01:29:53.439 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:29:53.459 [info] > git status -z -uall [8ms]
2025-06-05 01:29:53.462 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:29:55.941 [info] > git log --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z --shortstat --diff-merges=first-parent -n50 --skip=0 --topo-order --decorate=full --stdin [266ms]
2025-06-05 01:29:57.466 [info] > git log --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z --shortstat --diff-merges=first-parent -n50 --skip=0 --topo-order --decorate=full --stdin [66ms]
2025-06-05 01:29:58.480 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-05 01:29:58.481 [info] > git config --get commit.template [10ms]
2025-06-05 01:29:58.494 [info] > git status -z -uall [5ms]
2025-06-05 01:29:58.497 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:30:03.515 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:30:03.515 [info] > git config --get commit.template [6ms]
2025-06-05 01:30:03.522 [info] > git status -z -uall [3ms]
2025-06-05 01:30:03.523 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:32:10.505 [info] > git config --get commit.template [1ms]
2025-06-05 01:32:10.537 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:32:10.592 [info] > git status -z -uall [31ms]
2025-06-05 01:32:10.594 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:32:15.621 [info] > git config --get commit.template [8ms]
2025-06-05 01:32:15.625 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-05 01:32:15.643 [info] > git status -z -uall [6ms]
2025-06-05 01:32:15.646 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:32:20.670 [info] > git config --get commit.template [5ms]
2025-06-05 01:32:20.686 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:32:20.717 [info] > git status -z -uall [10ms]
2025-06-05 01:32:20.736 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [21ms]
2025-06-05 01:32:52.924 [info] > git config --get commit.template [0ms]
2025-06-05 01:32:52.935 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:32:52.955 [info] > git status -z -uall [8ms]
2025-06-05 01:32:52.960 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-05 01:32:58.056 [info] > git config --get commit.template [10ms]
2025-06-05 01:32:58.064 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [12ms]
2025-06-05 01:32:58.086 [info] > git status -z -uall [9ms]
2025-06-05 01:32:58.089 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:33:03.103 [info] > git config --get commit.template [1ms]
2025-06-05 01:33:03.111 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:33:03.133 [info] > git status -z -uall [6ms]
2025-06-05 01:33:03.133 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:33:08.159 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:33:08.159 [info] > git config --get commit.template [13ms]
2025-06-05 01:33:08.176 [info] > git status -z -uall [9ms]
2025-06-05 01:33:08.177 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:33:13.198 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:33:13.199 [info] > git config --get commit.template [9ms]
2025-06-05 01:33:13.214 [info] > git status -z -uall [9ms]
2025-06-05 01:33:13.217 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:33:18.230 [info] > git config --get commit.template [0ms]
2025-06-05 01:33:18.246 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:33:18.257 [info] > git status -z -uall [6ms]
2025-06-05 01:33:18.260 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:33:23.278 [info] > git config --get commit.template [9ms]
2025-06-05 01:33:23.280 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:33:23.304 [info] > git status -z -uall [15ms]
2025-06-05 01:33:23.304 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:33:28.322 [info] > git config --get commit.template [0ms]
2025-06-05 01:33:28.330 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:33:28.345 [info] > git status -z -uall [9ms]
2025-06-05 01:33:28.346 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:33:33.373 [info] > git config --get commit.template [12ms]
2025-06-05 01:33:33.386 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:33:33.412 [info] > git status -z -uall [17ms]
2025-06-05 01:33:33.413 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:33:38.433 [info] > git config --get commit.template [9ms]
2025-06-05 01:33:38.443 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-05 01:33:38.466 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:33:38.480 [info] > git status -z -uall [25ms]
2025-06-05 01:33:43.507 [info] > git config --get commit.template [11ms]
2025-06-05 01:33:43.508 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:33:43.518 [info] > git status -z -uall [4ms]
2025-06-05 01:33:43.523 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:33:48.536 [info] > git config --get commit.template [2ms]
2025-06-05 01:33:48.545 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:33:48.580 [info] > git status -z -uall [15ms]
2025-06-05 01:33:48.582 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:33:53.611 [info] > git config --get commit.template [18ms]
2025-06-05 01:33:53.616 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-05 01:33:53.659 [info] > git status -z -uall [26ms]
2025-06-05 01:33:53.659 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:33:58.679 [info] > git config --get commit.template [3ms]
2025-06-05 01:33:58.690 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:33:58.712 [info] > git status -z -uall [12ms]
2025-06-05 01:33:58.713 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:34:03.730 [info] > git config --get commit.template [1ms]
2025-06-05 01:34:03.740 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:34:03.767 [info] > git status -z -uall [18ms]
2025-06-05 01:34:03.768 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-05 01:34:08.783 [info] > git config --get commit.template [2ms]
2025-06-05 01:34:08.792 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:34:08.807 [info] > git status -z -uall [6ms]
2025-06-05 01:34:08.809 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:34:13.823 [info] > git config --get commit.template [3ms]
2025-06-05 01:34:13.840 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-05 01:34:13.861 [info] > git status -z -uall [11ms]
2025-06-05 01:34:13.862 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:34:18.885 [info] > git config --get commit.template [6ms]
2025-06-05 01:34:18.886 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:34:18.904 [info] > git status -z -uall [7ms]
2025-06-05 01:34:18.906 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:34:23.922 [info] > git config --get commit.template [0ms]
2025-06-05 01:34:23.937 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:34:23.951 [info] > git status -z -uall [8ms]
2025-06-05 01:34:23.953 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:34:28.967 [info] > git config --get commit.template [2ms]
2025-06-05 01:34:28.978 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:34:29.002 [info] > git status -z -uall [17ms]
2025-06-05 01:34:29.005 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-05 01:34:37.070 [info] > git config --get commit.template [6ms]
2025-06-05 01:34:37.072 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:34:37.088 [info] > git status -z -uall [10ms]
2025-06-05 01:34:37.091 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-05 01:34:42.114 [info] > git config --get commit.template [10ms]
2025-06-05 01:34:42.115 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:34:42.130 [info] > git status -z -uall [8ms]
2025-06-05 01:34:42.131 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:34:47.150 [info] > git config --get commit.template [10ms]
2025-06-05 01:34:47.153 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:34:47.170 [info] > git status -z -uall [10ms]
2025-06-05 01:34:47.171 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:34:52.188 [info] > git config --get commit.template [6ms]
2025-06-05 01:34:52.188 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:34:52.198 [info] > git status -z -uall [6ms]
2025-06-05 01:34:52.199 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:34:57.218 [info] > git config --get commit.template [10ms]
2025-06-05 01:34:57.222 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:34:57.231 [info] > git status -z -uall [4ms]
2025-06-05 01:34:57.233 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:35:02.247 [info] > git config --get commit.template [6ms]
2025-06-05 01:35:02.248 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:35:02.259 [info] > git status -z -uall [6ms]
2025-06-05 01:35:02.260 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:35:07.275 [info] > git config --get commit.template [6ms]
2025-06-05 01:35:07.276 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:35:07.289 [info] > git status -z -uall [7ms]
2025-06-05 01:35:07.290 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:35:12.301 [info] > git config --get commit.template [2ms]
2025-06-05 01:35:12.310 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:35:12.322 [info] > git status -z -uall [6ms]
2025-06-05 01:35:12.324 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:35:17.496 [info] > git config --get commit.template [161ms]
2025-06-05 01:35:17.505 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:35:17.536 [info] > git status -z -uall [8ms]
2025-06-05 01:35:17.542 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-05 01:35:22.567 [info] > git config --get commit.template [9ms]
2025-06-05 01:35:22.572 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-05 01:35:22.599 [info] > git status -z -uall [11ms]
2025-06-05 01:35:22.601 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:35:23.744 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-05 01:35:36.730 [info] > git config --get commit.template [6ms]
2025-06-05 01:35:36.731 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:35:36.753 [info] > git status -z -uall [13ms]
2025-06-05 01:35:36.754 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:35:41.771 [info] > git config --get commit.template [5ms]
2025-06-05 01:35:41.773 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:35:41.782 [info] > git status -z -uall [4ms]
2025-06-05 01:35:41.783 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:35:46.802 [info] > git config --get commit.template [6ms]
2025-06-05 01:35:46.802 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:35:46.828 [info] > git status -z -uall [8ms]
2025-06-05 01:35:46.829 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:35:51.844 [info] > git config --get commit.template [5ms]
2025-06-05 01:35:51.845 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:35:51.855 [info] > git status -z -uall [6ms]
2025-06-05 01:35:51.856 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:35:56.869 [info] > git config --get commit.template [5ms]
2025-06-05 01:35:56.870 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:35:56.878 [info] > git status -z -uall [4ms]
2025-06-05 01:35:56.879 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:35:58.386 [info] > git log --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z --shortstat --diff-merges=first-parent -n50 --skip=0 --topo-order --decorate=full --stdin [55ms]
2025-06-05 01:36:01.903 [info] > git config --get commit.template [7ms]
2025-06-05 01:36:01.904 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:36:01.916 [info] > git status -z -uall [5ms]
2025-06-05 01:36:01.917 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:36:06.987 [info] > git config --get commit.template [6ms]
2025-06-05 01:36:06.991 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-05 01:36:07.001 [info] > git status -z -uall [6ms]
2025-06-05 01:36:07.002 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:36:12.013 [info] > git config --get commit.template [4ms]
2025-06-05 01:36:12.014 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:36:12.022 [info] > git status -z -uall [5ms]
2025-06-05 01:36:12.023 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:36:17.039 [info] > git config --get commit.template [6ms]
2025-06-05 01:36:17.040 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:36:17.051 [info] > git status -z -uall [6ms]
2025-06-05 01:36:17.053 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:36:22.068 [info] > git config --get commit.template [6ms]
2025-06-05 01:36:22.069 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:36:22.078 [info] > git status -z -uall [3ms]
2025-06-05 01:36:22.079 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:36:27.091 [info] > git config --get commit.template [4ms]
2025-06-05 01:36:27.100 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:36:27.113 [info] > git status -z -uall [5ms]
2025-06-05 01:36:27.114 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:36:32.127 [info] > git config --get commit.template [0ms]
2025-06-05 01:36:32.133 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:36:32.143 [info] > git status -z -uall [6ms]
2025-06-05 01:36:32.144 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:36:37.160 [info] > git config --get commit.template [5ms]
2025-06-05 01:36:37.161 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:36:37.170 [info] > git status -z -uall [4ms]
2025-06-05 01:36:37.171 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:36:42.182 [info] > git config --get commit.template [0ms]
2025-06-05 01:36:42.189 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:36:42.198 [info] > git status -z -uall [5ms]
2025-06-05 01:36:42.199 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:36:47.216 [info] > git config --get commit.template [7ms]
2025-06-05 01:36:47.217 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:36:47.227 [info] > git status -z -uall [5ms]
2025-06-05 01:36:47.228 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:36:53.726 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:36:53.726 [info] > git config --get commit.template [23ms]
2025-06-05 01:36:53.747 [info] > git status -z -uall [8ms]
2025-06-05 01:36:53.749 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:36:58.761 [info] > git config --get commit.template [0ms]
2025-06-05 01:36:58.771 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-05 01:36:58.791 [info] > git status -z -uall [13ms]
2025-06-05 01:36:58.791 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:37:03.814 [info] > git config --get commit.template [9ms]
2025-06-05 01:37:03.815 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:37:03.827 [info] > git status -z -uall [6ms]
2025-06-05 01:37:03.828 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:37:08.860 [info] > git config --get commit.template [14ms]
2025-06-05 01:37:08.861 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:37:08.877 [info] > git status -z -uall [8ms]
2025-06-05 01:37:08.878 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:37:17.035 [info] > git config --get commit.template [6ms]
2025-06-05 01:37:17.036 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:37:17.047 [info] > git status -z -uall [7ms]
2025-06-05 01:37:17.048 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:37:22.064 [info] > git config --get commit.template [6ms]
2025-06-05 01:37:22.065 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:37:22.077 [info] > git status -z -uall [6ms]
2025-06-05 01:37:22.077 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:37:27.872 [info] > git config --get commit.template [4ms]
2025-06-05 01:37:27.883 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:37:27.911 [info] > git status -z -uall [11ms]
2025-06-05 01:37:27.915 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:37:32.930 [info] > git config --get commit.template [2ms]
2025-06-05 01:37:32.940 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:37:32.951 [info] > git status -z -uall [4ms]
2025-06-05 01:37:32.952 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:37:37.967 [info] > git config --get commit.template [6ms]
2025-06-05 01:37:37.968 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:37:37.977 [info] > git status -z -uall [5ms]
2025-06-05 01:37:37.978 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:37:42.999 [info] > git config --get commit.template [5ms]
2025-06-05 01:37:43.000 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:37:43.013 [info] > git status -z -uall [8ms]
2025-06-05 01:37:43.013 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:37:48.042 [info] > git config --get commit.template [9ms]
2025-06-05 01:37:48.043 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:37:48.054 [info] > git status -z -uall [5ms]
2025-06-05 01:37:48.055 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:37:53.076 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-05 01:37:53.076 [info] > git config --get commit.template [12ms]
2025-06-05 01:37:53.090 [info] > git status -z -uall [6ms]
2025-06-05 01:37:53.092 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:37:58.107 [info] > git config --get commit.template [6ms]
2025-06-05 01:37:58.108 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:37:58.119 [info] > git status -z -uall [5ms]
2025-06-05 01:37:58.120 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:38:03.134 [info] > git config --get commit.template [5ms]
2025-06-05 01:38:03.135 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:38:03.145 [info] > git status -z -uall [5ms]
2025-06-05 01:38:03.146 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:38:08.158 [info] > git config --get commit.template [3ms]
2025-06-05 01:38:08.167 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:38:08.176 [info] > git status -z -uall [4ms]
2025-06-05 01:38:08.178 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:38:13.192 [info] > git config --get commit.template [4ms]
2025-06-05 01:38:13.193 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:38:13.203 [info] > git status -z -uall [5ms]
2025-06-05 01:38:13.204 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:38:18.228 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:38:18.229 [info] > git config --get commit.template [13ms]
2025-06-05 01:38:18.242 [info] > git status -z -uall [5ms]
2025-06-05 01:38:18.243 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:38:23.260 [info] > git config --get commit.template [7ms]
2025-06-05 01:38:23.261 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:38:23.270 [info] > git status -z -uall [5ms]
2025-06-05 01:38:23.272 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:38:28.284 [info] > git config --get commit.template [4ms]
2025-06-05 01:38:28.285 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:38:28.292 [info] > git status -z -uall [4ms]
2025-06-05 01:38:28.294 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:38:33.309 [info] > git config --get commit.template [6ms]
2025-06-05 01:38:33.315 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-05 01:38:33.323 [info] > git status -z -uall [4ms]
2025-06-05 01:38:33.324 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:38:43.093 [info] > git config --get commit.template [3ms]
2025-06-05 01:38:43.097 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:38:43.107 [info] > git status -z -uall [6ms]
2025-06-05 01:38:43.107 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:38:48.126 [info] > git config --get commit.template [6ms]
2025-06-05 01:38:48.129 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:38:48.152 [info] > git status -z -uall [8ms]
2025-06-05 01:38:48.162 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-05 01:38:53.191 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:38:53.192 [info] > git config --get commit.template [12ms]
2025-06-05 01:38:53.202 [info] > git status -z -uall [4ms]
2025-06-05 01:38:53.203 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:39:57.479 [info] > git config --get commit.template [2ms]
2025-06-05 01:39:57.489 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-05 01:39:57.507 [info] > git status -z -uall [9ms]
2025-06-05 01:39:57.509 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:40:02.538 [info] > git config --get commit.template [14ms]
2025-06-05 01:40:02.539 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:40:02.553 [info] > git status -z -uall [6ms]
2025-06-05 01:40:02.554 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:40:07.570 [info] > git config --get commit.template [4ms]
2025-06-05 01:40:07.617 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [19ms]
2025-06-05 01:40:07.640 [info] > git status -z -uall [13ms]
2025-06-05 01:40:07.643 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:40:12.659 [info] > git config --get commit.template [0ms]
2025-06-05 01:40:12.667 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:40:12.682 [info] > git status -z -uall [8ms]
2025-06-05 01:40:12.683 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:40:17.695 [info] > git config --get commit.template [2ms]
2025-06-05 01:40:17.712 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:40:17.731 [info] > git status -z -uall [7ms]
2025-06-05 01:40:17.731 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:40:22.747 [info] > git config --get commit.template [6ms]
2025-06-05 01:40:22.748 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:40:22.757 [info] > git status -z -uall [5ms]
2025-06-05 01:40:22.758 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:40:27.780 [info] > git config --get commit.template [7ms]
2025-06-05 01:40:27.780 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:40:27.801 [info] > git status -z -uall [14ms]
2025-06-05 01:40:27.802 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:40:32.816 [info] > git config --get commit.template [1ms]
2025-06-05 01:40:32.831 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:40:32.867 [info] > git status -z -uall [23ms]
2025-06-05 01:40:32.869 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:40:37.888 [info] > git config --get commit.template [5ms]
2025-06-05 01:40:37.889 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:40:37.900 [info] > git status -z -uall [5ms]
2025-06-05 01:40:37.901 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:40:42.922 [info] > git config --get commit.template [10ms]
2025-06-05 01:40:42.924 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:40:42.958 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:40:42.968 [info] > git status -z -uall [22ms]
2025-06-05 01:40:47.996 [info] > git config --get commit.template [7ms]
2025-06-05 01:40:47.997 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:40:48.013 [info] > git status -z -uall [10ms]
2025-06-05 01:40:48.013 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-05 01:40:53.122 [info] > git config --get commit.template [0ms]
2025-06-05 01:40:53.131 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:40:53.144 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:40:53.146 [info] > git status -z -uall [9ms]
2025-06-05 01:41:00.454 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-05 01:41:01.589 [info] > git config --get commit.template [22ms]
2025-06-05 01:41:01.592 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-05 01:41:01.643 [info] > git status -z -uall [38ms]
2025-06-05 01:41:01.643 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:41:06.659 [info] > git config --get commit.template [5ms]
2025-06-05 01:41:06.659 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:41:06.668 [info] > git status -z -uall [4ms]
2025-06-05 01:41:06.669 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:41:11.686 [info] > git config --get commit.template [7ms]
2025-06-05 01:41:11.687 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:41:11.701 [info] > git status -z -uall [7ms]
2025-06-05 01:41:11.703 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:41:16.869 [info] > git config --get commit.template [82ms]
2025-06-05 01:41:16.933 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [21ms]
2025-06-05 01:41:17.167 [info] > git status -z -uall [60ms]
2025-06-05 01:41:17.167 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [49ms]
2025-06-05 01:41:21.274 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-05 01:41:22.183 [info] > git config --get commit.template [0ms]
2025-06-05 01:41:22.193 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:41:22.229 [info] > git status -z -uall [28ms]
2025-06-05 01:41:22.243 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [35ms]
2025-06-05 01:41:27.293 [info] > git config --get commit.template [36ms]
2025-06-05 01:41:27.296 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:41:27.333 [info] > git status -z -uall [14ms]
2025-06-05 01:41:27.336 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:41:32.362 [info] > git config --get commit.template [11ms]
2025-06-05 01:41:32.365 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-05 01:41:32.425 [info] > git status -z -uall [35ms]
2025-06-05 01:41:32.426 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:41:37.491 [info] > git config --get commit.template [54ms]
2025-06-05 01:41:37.651 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [161ms]
2025-06-05 01:41:37.672 [info] > git status -z -uall [11ms]
2025-06-05 01:41:37.676 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-05 01:41:41.701 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-05 01:41:42.706 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:41:42.706 [info] > git config --get commit.template [16ms]
2025-06-05 01:41:42.731 [info] > git status -z -uall [16ms]
2025-06-05 01:41:42.735 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:41:44.000 [info] > git show --textconv :server/openai-realtime-proxy.ts [106ms]
2025-06-05 01:41:44.000 [info] > git ls-files --stage -- server/openai-realtime-proxy.ts [95ms]
2025-06-05 01:41:44.014 [info] > git cat-file -s 84f556334c9e9880843d9272c91f6cbb1e594c92 [3ms]
2025-06-05 01:41:47.753 [info] > git config --get commit.template [5ms]
2025-06-05 01:41:47.754 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:41:47.762 [info] > git status -z -uall [4ms]
2025-06-05 01:41:47.764 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:41:52.784 [info] > git config --get commit.template [9ms]
2025-06-05 01:41:52.787 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:41:52.812 [info] > git status -z -uall [17ms]
2025-06-05 01:41:52.813 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:41:52.979 [info] > git blame --root --incremental 40f156400ee62158de598c268b329e23343defd1 -- server/openai-realtime-proxy.ts [3ms]
2025-06-05 01:41:57.862 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:41:57.863 [info] > git config --get commit.template [33ms]
2025-06-05 01:41:57.914 [info] > git status -z -uall [22ms]
2025-06-05 01:41:57.917 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:42:02.942 [info] > git config --get commit.template [1ms]
2025-06-05 01:42:02.954 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:42:02.979 [info] > git status -z -uall [15ms]
2025-06-05 01:42:02.980 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-05 01:42:08.001 [info] > git config --get commit.template [8ms]
2025-06-05 01:42:08.012 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:42:08.056 [info] > git status -z -uall [30ms]
2025-06-05 01:42:08.056 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:42:13.098 [info] > git config --get commit.template [9ms]
2025-06-05 01:42:13.101 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:42:13.125 [info] > git status -z -uall [15ms]
2025-06-05 01:42:13.127 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:42:18.147 [info] > git config --get commit.template [8ms]
2025-06-05 01:42:18.148 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:42:18.161 [info] > git status -z -uall [6ms]
2025-06-05 01:42:18.165 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:42:23.199 [info] > git config --get commit.template [17ms]
2025-06-05 01:42:23.200 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:42:23.220 [info] > git status -z -uall [11ms]
2025-06-05 01:42:23.221 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:42:28.234 [info] > git config --get commit.template [4ms]
2025-06-05 01:42:28.235 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:42:28.242 [info] > git status -z -uall [3ms]
2025-06-05 01:42:28.243 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:42:33.255 [info] > git config --get commit.template [0ms]
2025-06-05 01:42:33.263 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:42:33.273 [info] > git status -z -uall [5ms]
2025-06-05 01:42:33.275 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:42:38.289 [info] > git config --get commit.template [5ms]
2025-06-05 01:42:38.290 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:42:38.299 [info] > git status -z -uall [4ms]
2025-06-05 01:42:38.300 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:42:43.316 [info] > git config --get commit.template [5ms]
2025-06-05 01:42:43.316 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:42:43.325 [info] > git status -z -uall [4ms]
2025-06-05 01:42:43.327 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:42:48.345 [info] > git config --get commit.template [7ms]
2025-06-05 01:42:48.348 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:42:48.374 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:42:48.377 [info] > git status -z -uall [11ms]
2025-06-05 01:42:53.392 [info] > git config --get commit.template [6ms]
2025-06-05 01:42:53.393 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:42:53.414 [info] > git status -z -uall [11ms]
2025-06-05 01:42:53.415 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:42:58.440 [info] > git config --get commit.template [11ms]
2025-06-05 01:42:58.445 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-05 01:42:58.466 [info] > git status -z -uall [12ms]
2025-06-05 01:42:58.467 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:43:03.487 [info] > git config --get commit.template [9ms]
2025-06-05 01:43:03.488 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:43:03.502 [info] > git status -z -uall [8ms]
2025-06-05 01:43:03.505 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:43:08.540 [info] > git config --get commit.template [17ms]
2025-06-05 01:43:08.542 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:43:08.576 [info] > git status -z -uall [13ms]
2025-06-05 01:43:08.577 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:43:13.599 [info] > git config --get commit.template [5ms]
2025-06-05 01:43:13.602 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:43:13.615 [info] > git status -z -uall [5ms]
2025-06-05 01:43:13.619 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:43:18.643 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:43:18.643 [info] > git config --get commit.template [11ms]
2025-06-05 01:43:18.656 [info] > git status -z -uall [5ms]
2025-06-05 01:43:18.657 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:43:23.670 [info] > git config --get commit.template [4ms]
2025-06-05 01:43:23.671 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:43:23.685 [info] > git status -z -uall [6ms]
2025-06-05 01:43:23.686 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:43:33.794 [info] > git config --get commit.template [6ms]
2025-06-05 01:43:33.796 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:43:33.807 [info] > git status -z -uall [5ms]
2025-06-05 01:43:33.809 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:43:38.825 [info] > git config --get commit.template [6ms]
2025-06-05 01:43:38.826 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:43:38.836 [info] > git status -z -uall [5ms]
2025-06-05 01:43:38.837 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:43:43.851 [info] > git config --get commit.template [4ms]
2025-06-05 01:43:43.852 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:43:43.860 [info] > git status -z -uall [4ms]
2025-06-05 01:43:43.861 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:44:54.752 [info] > git config --get commit.template [0ms]
2025-06-05 01:44:54.763 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:44:54.776 [info] > git status -z -uall [7ms]
2025-06-05 01:44:54.779 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:44:59.797 [info] > git config --get commit.template [6ms]
2025-06-05 01:44:59.798 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:44:59.807 [info] > git status -z -uall [5ms]
2025-06-05 01:44:59.809 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:45:04.824 [info] > git config --get commit.template [6ms]
2025-06-05 01:45:04.828 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:45:04.845 [info] > git status -z -uall [11ms]
2025-06-05 01:45:04.846 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:45:21.356 [info] > git config --get commit.template [5ms]
2025-06-05 01:45:21.356 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:45:21.364 [info] > git status -z -uall [4ms]
2025-06-05 01:45:21.366 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:45:26.384 [info] > git config --get commit.template [6ms]
2025-06-05 01:45:26.386 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:45:26.401 [info] > git status -z -uall [11ms]
2025-06-05 01:45:26.401 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:45:31.418 [info] > git config --get commit.template [5ms]
2025-06-05 01:45:31.419 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:45:31.434 [info] > git status -z -uall [8ms]
2025-06-05 01:45:31.435 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:45:36.470 [info] > git config --get commit.template [14ms]
2025-06-05 01:45:36.471 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:45:36.496 [info] > git status -z -uall [13ms]
2025-06-05 01:45:36.498 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:45:41.529 [info] > git config --get commit.template [11ms]
2025-06-05 01:45:41.530 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:45:41.546 [info] > git status -z -uall [7ms]
2025-06-05 01:45:41.550 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:45:46.573 [info] > git config --get commit.template [9ms]
2025-06-05 01:45:46.574 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:45:46.587 [info] > git status -z -uall [5ms]
2025-06-05 01:45:46.590 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:45:51.606 [info] > git config --get commit.template [5ms]
2025-06-05 01:45:51.607 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:45:51.619 [info] > git status -z -uall [6ms]
2025-06-05 01:45:51.620 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:45:56.635 [info] > git config --get commit.template [5ms]
2025-06-05 01:45:56.636 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:45:56.646 [info] > git status -z -uall [7ms]
2025-06-05 01:45:56.655 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-05 01:46:01.669 [info] > git config --get commit.template [6ms]
2025-06-05 01:46:01.670 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:46:01.681 [info] > git status -z -uall [5ms]
2025-06-05 01:46:01.682 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:46:06.695 [info] > git config --get commit.template [4ms]
2025-06-05 01:46:06.696 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:46:06.706 [info] > git status -z -uall [5ms]
2025-06-05 01:46:06.707 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:46:11.719 [info] > git config --get commit.template [4ms]
2025-06-05 01:46:11.720 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:46:11.728 [info] > git status -z -uall [4ms]
2025-06-05 01:46:11.729 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:46:27.926 [info] > git config --get commit.template [6ms]
2025-06-05 01:46:27.927 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:46:27.937 [info] > git status -z -uall [4ms]
2025-06-05 01:46:27.938 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:46:32.961 [info] > git config --get commit.template [7ms]
2025-06-05 01:46:32.964 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:46:32.982 [info] > git status -z -uall [8ms]
2025-06-05 01:46:32.983 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:46:35.326 [info] > git log --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z --shortstat --diff-merges=first-parent -n50 --skip=0 --topo-order --decorate=full --stdin [50ms]
2025-06-05 01:46:35.795 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-05 01:46:38.014 [info] > git config --get commit.template [5ms]
2025-06-05 01:46:38.015 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:46:38.026 [info] > git status -z -uall [6ms]
2025-06-05 01:46:38.027 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:46:38.657 [info] > git show --textconv HEAD:server/openai-realtime-proxy.ts [6ms]
2025-06-05 01:46:38.658 [info] > git ls-tree -l HEAD -- server/openai-realtime-proxy.ts [2ms]
2025-06-05 01:46:43.048 [info] > git config --get commit.template [1ms]
2025-06-05 01:46:43.061 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:46:43.089 [info] > git status -z -uall [14ms]
2025-06-05 01:46:43.096 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-05 01:46:48.110 [info] > git config --get commit.template [6ms]
2025-06-05 01:46:48.111 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:46:48.119 [info] > git status -z -uall [4ms]
2025-06-05 01:46:48.120 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:46:53.138 [info] > git config --get commit.template [6ms]
2025-06-05 01:46:53.139 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:46:53.151 [info] > git status -z -uall [6ms]
2025-06-05 01:46:53.154 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:46:58.167 [info] > git config --get commit.template [1ms]
2025-06-05 01:46:58.173 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:46:58.183 [info] > git status -z -uall [5ms]
2025-06-05 01:46:58.184 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:47:03.200 [info] > git config --get commit.template [6ms]
2025-06-05 01:47:03.201 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:47:03.210 [info] > git status -z -uall [4ms]
2025-06-05 01:47:03.211 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:47:08.228 [info] > git config --get commit.template [6ms]
2025-06-05 01:47:08.230 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:47:08.239 [info] > git status -z -uall [4ms]
2025-06-05 01:47:08.240 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:47:13.251 [info] > git config --get commit.template [3ms]
2025-06-05 01:47:13.258 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:47:13.278 [info] > git status -z -uall [8ms]
2025-06-05 01:47:13.279 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:47:18.293 [info] > git config --get commit.template [6ms]
2025-06-05 01:47:18.294 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:47:18.302 [info] > git status -z -uall [4ms]
2025-06-05 01:47:18.303 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:47:23.320 [info] > git config --get commit.template [7ms]
2025-06-05 01:47:23.321 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:47:23.331 [info] > git status -z -uall [5ms]
2025-06-05 01:47:23.332 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:47:28.352 [info] > git config --get commit.template [9ms]
2025-06-05 01:47:28.357 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-05 01:47:28.370 [info] > git status -z -uall [7ms]
2025-06-05 01:47:28.375 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-05 01:47:33.395 [info] > git config --get commit.template [7ms]
2025-06-05 01:47:33.398 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:47:33.436 [info] > git status -z -uall [17ms]
2025-06-05 01:47:33.441 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:47:38.466 [info] > git config --get commit.template [8ms]
2025-06-05 01:47:38.471 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-05 01:47:38.497 [info] > git status -z -uall [13ms]
2025-06-05 01:47:38.498 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-05 01:48:52.784 [info] > git ls-tree -l HEAD -- server/openai-realtime-proxy.ts [29ms]
2025-06-05 01:48:52.786 [info] > git config --get commit.template [2ms]
2025-06-05 01:48:52.801 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:48:52.836 [info] > git status -z -uall [25ms]
2025-06-05 01:48:52.840 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:48:57.872 [info] > git config --get commit.template [12ms]
2025-06-05 01:48:57.873 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-05 01:48:57.892 [info] > git status -z -uall [11ms]
2025-06-05 01:48:57.895 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:49:02.920 [info] > git config --get commit.template [12ms]
2025-06-05 01:49:02.922 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:49:02.936 [info] > git status -z -uall [7ms]
2025-06-05 01:49:02.937 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:49:08.033 [info] > git config --get commit.template [85ms]
2025-06-05 01:49:08.034 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [73ms]
2025-06-05 01:49:08.085 [info] > git status -z -uall [23ms]
2025-06-05 01:49:08.085 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:49:13.139 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:49:13.139 [info] > git config --get commit.template [32ms]
2025-06-05 01:49:13.177 [info] > git status -z -uall [27ms]
2025-06-05 01:49:13.181 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-05 01:49:18.220 [info] > git config --get commit.template [15ms]
2025-06-05 01:49:18.222 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:49:18.251 [info] > git status -z -uall [15ms]
2025-06-05 01:49:18.251 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:49:23.265 [info] > git config --get commit.template [2ms]
2025-06-05 01:49:23.281 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:49:23.304 [info] > git status -z -uall [10ms]
2025-06-05 01:49:23.305 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:49:28.317 [info] > git config --get commit.template [0ms]
2025-06-05 01:49:28.328 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:49:28.370 [info] > git status -z -uall [22ms]
2025-06-05 01:49:28.370 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:49:33.381 [info] > git config --get commit.template [2ms]
2025-06-05 01:49:33.391 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:49:33.409 [info] > git status -z -uall [9ms]
2025-06-05 01:49:33.411 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:49:38.441 [info] > git config --get commit.template [11ms]
2025-06-05 01:49:38.443 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:49:38.468 [info] > git status -z -uall [14ms]
2025-06-05 01:49:38.473 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-05 01:49:43.498 [info] > git config --get commit.template [12ms]
2025-06-05 01:49:43.499 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-05 01:49:43.520 [info] > git status -z -uall [9ms]
2025-06-05 01:49:43.523 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:49:48.558 [info] > git config --get commit.template [7ms]
2025-06-05 01:49:48.560 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:49:48.582 [info] > git status -z -uall [10ms]
2025-06-05 01:49:48.584 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:49:53.607 [info] > git config --get commit.template [7ms]
2025-06-05 01:49:53.610 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-05 01:49:53.643 [info] > git status -z -uall [15ms]
2025-06-05 01:49:53.645 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:49:58.664 [info] > git config --get commit.template [3ms]
2025-06-05 01:49:58.679 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:49:58.703 [info] > git status -z -uall [15ms]
2025-06-05 01:49:58.705 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:50:03.720 [info] > git config --get commit.template [1ms]
2025-06-05 01:50:03.732 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-05 01:50:03.756 [info] > git status -z -uall [12ms]
2025-06-05 01:50:03.758 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:50:08.771 [info] > git config --get commit.template [3ms]
2025-06-05 01:50:08.779 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:50:08.802 [info] > git status -z -uall [11ms]
2025-06-05 01:50:08.802 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:50:13.822 [info] > git config --get commit.template [0ms]
2025-06-05 01:50:13.832 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:50:13.863 [info] > git status -z -uall [16ms]
2025-06-05 01:50:13.864 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-05 01:50:18.904 [info] > git config --get commit.template [9ms]
2025-06-05 01:50:18.906 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:50:18.923 [info] > git status -z -uall [9ms]
2025-06-05 01:50:18.924 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:50:23.944 [info] > git config --get commit.template [6ms]
2025-06-05 01:50:23.945 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:50:23.959 [info] > git status -z -uall [8ms]
2025-06-05 01:50:23.969 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-05 01:50:29.062 [info] > git config --get commit.template [2ms]
2025-06-05 01:50:29.077 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-05 01:50:29.099 [info] > git status -z -uall [14ms]
2025-06-05 01:50:29.099 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:50:34.111 [info] > git config --get commit.template [2ms]
2025-06-05 01:50:34.121 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-05 01:50:34.132 [info] > git status -z -uall [5ms]
2025-06-05 01:50:34.133 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:50:39.148 [info] > git config --get commit.template [5ms]
2025-06-05 01:50:39.149 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:50:39.157 [info] > git status -z -uall [3ms]
2025-06-05 01:50:39.158 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:50:48.291 [info] > git ls-tree -l HEAD -- server/openai-realtime-proxy.ts [6ms]
2025-06-05 01:50:48.295 [info] > git config --get commit.template [5ms]
2025-06-05 01:50:48.296 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:50:48.305 [info] > git status -z -uall [4ms]
2025-06-05 01:50:48.305 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:50:53.321 [info] > git config --get commit.template [6ms]
2025-06-05 01:50:53.322 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:50:53.330 [info] > git status -z -uall [4ms]
2025-06-05 01:50:53.331 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:50:58.343 [info] > git config --get commit.template [4ms]
2025-06-05 01:50:58.344 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:50:58.354 [info] > git status -z -uall [4ms]
2025-06-05 01:50:58.355 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:51:03.372 [info] > git config --get commit.template [8ms]
2025-06-05 01:51:03.374 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:51:03.391 [info] > git status -z -uall [9ms]
2025-06-05 01:51:03.392 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:51:08.404 [info] > git config --get commit.template [0ms]
2025-06-05 01:51:08.409 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:51:08.421 [info] > git status -z -uall [6ms]
2025-06-05 01:51:08.423 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:51:13.435 [info] > git config --get commit.template [2ms]
2025-06-05 01:51:13.446 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:51:13.482 [info] > git status -z -uall [20ms]
2025-06-05 01:51:13.483 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:51:18.498 [info] > git config --get commit.template [6ms]
2025-06-05 01:51:18.499 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:51:18.508 [info] > git status -z -uall [5ms]
2025-06-05 01:51:18.509 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:51:23.524 [info] > git config --get commit.template [5ms]
2025-06-05 01:51:23.525 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:51:23.537 [info] > git status -z -uall [5ms]
2025-06-05 01:51:23.538 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:51:29.489 [info] > git config --get commit.template [9ms]
2025-06-05 01:51:29.492 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:51:29.514 [info] > git status -z -uall [13ms]
2025-06-05 01:51:29.518 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-05 01:51:34.584 [info] > git config --get commit.template [8ms]
2025-06-05 01:51:34.585 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:51:34.592 [info] > git status -z -uall [3ms]
2025-06-05 01:51:34.593 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:51:35.622 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-05 01:51:37.285 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-05 01:51:39.610 [info] > git config --get commit.template [7ms]
2025-06-05 01:51:39.611 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:51:39.620 [info] > git status -z -uall [5ms]
2025-06-05 01:51:39.621 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:51:44.638 [info] > git config --get commit.template [7ms]
2025-06-05 01:51:44.639 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:51:44.650 [info] > git status -z -uall [4ms]
2025-06-05 01:51:44.651 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:51:49.678 [info] > git config --get commit.template [6ms]
2025-06-05 01:51:49.679 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:51:49.692 [info] > git status -z -uall [8ms]
2025-06-05 01:51:49.693 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:51:54.724 [info] > git config --get commit.template [20ms]
2025-06-05 01:51:54.751 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [31ms]
2025-06-05 01:51:54.781 [info] > git status -z -uall [10ms]
2025-06-05 01:51:54.782 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:51:59.801 [info] > git config --get commit.template [6ms]
2025-06-05 01:51:59.805 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-05 01:51:59.836 [info] > git status -z -uall [15ms]
2025-06-05 01:51:59.837 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:52:04.863 [info] > git config --get commit.template [13ms]
2025-06-05 01:52:04.865 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:52:04.887 [info] > git status -z -uall [9ms]
2025-06-05 01:52:04.888 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:52:05.327 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-05 01:52:09.909 [info] > git config --get commit.template [3ms]
2025-06-05 01:52:09.924 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:52:09.946 [info] > git status -z -uall [11ms]
2025-06-05 01:52:09.947 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:52:15.000 [info] > git config --get commit.template [26ms]
2025-06-05 01:52:15.008 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-05 01:52:15.053 [info] > git status -z -uall [20ms]
2025-06-05 01:52:15.067 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [15ms]
2025-06-05 01:52:21.616 [info] > git config --get commit.template [2ms]
2025-06-05 01:52:21.631 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-05 01:52:21.657 [info] > git status -z -uall [9ms]
2025-06-05 01:52:21.662 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:52:26.678 [info] > git config --get commit.template [0ms]
2025-06-05 01:52:26.687 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:52:26.696 [info] > git status -z -uall [4ms]
2025-06-05 01:52:26.701 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:52:31.724 [info] > git config --get commit.template [3ms]
2025-06-05 01:52:31.754 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-05 01:52:31.808 [info] > git status -z -uall [21ms]
2025-06-05 01:52:31.813 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-05 01:52:36.845 [info] > git config --get commit.template [11ms]
2025-06-05 01:52:36.847 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:52:36.876 [info] > git status -z -uall [17ms]
2025-06-05 01:52:36.879 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:52:41.895 [info] > git config --get commit.template [0ms]
2025-06-05 01:52:41.905 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:52:41.928 [info] > git status -z -uall [17ms]
2025-06-05 01:52:41.930 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:52:46.951 [info] > git config --get commit.template [7ms]
2025-06-05 01:52:46.952 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:52:46.963 [info] > git status -z -uall [6ms]
2025-06-05 01:52:46.965 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:52:51.985 [info] > git config --get commit.template [9ms]
2025-06-05 01:52:51.986 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:52:51.997 [info] > git status -z -uall [4ms]
2025-06-05 01:52:51.998 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:52:57.015 [info] > git config --get commit.template [7ms]
2025-06-05 01:52:57.017 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:52:57.038 [info] > git status -z -uall [10ms]
2025-06-05 01:52:57.039 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:53:02.087 [info] > git config --get commit.template [10ms]
2025-06-05 01:53:02.088 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:53:02.101 [info] > git status -z -uall [7ms]
2025-06-05 01:53:02.102 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:53:07.119 [info] > git config --get commit.template [2ms]
2025-06-05 01:53:07.132 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:53:07.171 [info] > git status -z -uall [27ms]
2025-06-05 01:53:07.173 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-05 01:53:12.196 [info] > git config --get commit.template [1ms]
2025-06-05 01:53:12.212 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:53:12.272 [info] > git status -z -uall [43ms]
2025-06-05 01:53:12.272 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [13ms]
2025-06-05 01:53:17.353 [info] > git config --get commit.template [9ms]
2025-06-05 01:53:17.355 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:53:17.387 [info] > git status -z -uall [22ms]
2025-06-05 01:53:17.387 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-05 01:53:22.403 [info] > git config --get commit.template [1ms]
2025-06-05 01:53:22.413 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:53:22.451 [info] > git status -z -uall [23ms]
2025-06-05 01:53:22.457 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-05 01:53:27.481 [info] > git config --get commit.template [9ms]
2025-06-05 01:53:27.483 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:53:27.495 [info] > git status -z -uall [7ms]
2025-06-05 01:53:27.496 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:53:32.512 [info] > git config --get commit.template [1ms]
2025-06-05 01:53:32.530 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:53:32.571 [info] > git status -z -uall [13ms]
2025-06-05 01:53:32.572 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:53:37.767 [info] > git config --get commit.template [24ms]
2025-06-05 01:53:37.768 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-05 01:53:37.796 [info] > git status -z -uall [16ms]
2025-06-05 01:53:37.798 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:53:42.865 [info] > git config --get commit.template [2ms]
2025-06-05 01:53:42.874 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:53:42.891 [info] > git status -z -uall [10ms]
2025-06-05 01:53:42.895 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:53:47.906 [info] > git config --get commit.template [1ms]
2025-06-05 01:53:47.933 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:53:47.948 [info] > git status -z -uall [7ms]
2025-06-05 01:53:47.949 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:53:52.969 [info] > git config --get commit.template [6ms]
2025-06-05 01:53:52.970 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:53:52.979 [info] > git status -z -uall [5ms]
2025-06-05 01:53:52.980 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:53:57.996 [info] > git config --get commit.template [6ms]
2025-06-05 01:53:57.999 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:53:58.018 [info] > git status -z -uall [10ms]
2025-06-05 01:53:58.032 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [15ms]
2025-06-05 01:54:03.052 [info] > git config --get commit.template [1ms]
2025-06-05 01:54:03.066 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-05 01:54:03.088 [info] > git status -z -uall [11ms]
2025-06-05 01:54:03.092 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:54:08.112 [info] > git config --get commit.template [9ms]
2025-06-05 01:54:08.113 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:54:08.135 [info] > git status -z -uall [17ms]
2025-06-05 01:54:08.135 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:54:13.158 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-05 01:54:13.159 [info] > git config --get commit.template [13ms]
2025-06-05 01:54:13.192 [info] > git status -z -uall [14ms]
2025-06-05 01:54:13.196 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:54:18.229 [info] > git config --get commit.template [14ms]
2025-06-05 01:54:18.229 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:54:18.262 [info] > git status -z -uall [13ms]
2025-06-05 01:54:18.264 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:54:23.286 [info] > git config --get commit.template [12ms]
2025-06-05 01:54:23.288 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:54:23.310 [info] > git status -z -uall [16ms]
2025-06-05 01:54:23.313 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:54:28.332 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-05 01:54:28.333 [info] > git config --get commit.template [9ms]
2025-06-05 01:54:28.346 [info] > git status -z -uall [5ms]
2025-06-05 01:54:28.347 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:54:33.376 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:54:33.376 [info] > git config --get commit.template [14ms]
2025-06-05 01:54:33.416 [info] > git status -z -uall [26ms]
2025-06-05 01:54:33.418 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:54:38.447 [info] > git config --get commit.template [0ms]
2025-06-05 01:54:38.472 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-05 01:54:38.501 [info] > git status -z -uall [7ms]
2025-06-05 01:54:38.504 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:54:43.546 [info] > git config --get commit.template [0ms]
2025-06-05 01:54:43.560 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:54:43.582 [info] > git status -z -uall [11ms]
2025-06-05 01:54:43.586 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:54:48.607 [info] > git config --get commit.template [8ms]
2025-06-05 01:54:48.608 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:54:48.619 [info] > git status -z -uall [6ms]
2025-06-05 01:54:48.620 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:54:53.645 [info] > git config --get commit.template [10ms]
2025-06-05 01:54:53.649 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-05 01:54:53.669 [info] > git status -z -uall [11ms]
2025-06-05 01:54:53.671 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-05 01:54:58.683 [info] > git config --get commit.template [4ms]
2025-06-05 01:54:58.684 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:54:58.693 [info] > git status -z -uall [5ms]
2025-06-05 01:54:58.694 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
