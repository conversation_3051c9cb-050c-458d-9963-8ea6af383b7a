2025-06-05 01:27:22.988 [info] [main] Log level: Info
2025-06-05 01:27:22.988 [info] [main] Validating found git in: "git"
2025-06-05 01:27:22.988 [info] [main] Using git "2.47.2" from "git"
2025-06-05 01:27:22.988 [info] [Model][doInitialScan] Initial repository scan started
2025-06-05 01:27:22.988 [info] > git rev-parse --show-toplevel [52ms]
2025-06-05 01:27:22.988 [info] > git rev-parse --git-dir --git-common-dir [5ms]
2025-06-05 01:27:22.988 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-05 01:27:22.989 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-05 01:27:22.989 [info] > git config --get commit.template [5ms]
2025-06-05 01:27:22.989 [info] > git rev-parse --show-toplevel [3ms]
2025-06-05 01:27:22.999 [info] > git rev-parse --show-toplevel [7ms]
2025-06-05 01:27:22.999 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [12ms]
2025-06-05 01:27:23.012 [info] > git rev-parse --show-toplevel [6ms]
2025-06-05 01:27:23.032 [info] > git status -z -uall [12ms]
2025-06-05 01:27:23.033 [info] > git rev-parse --show-toplevel [17ms]
2025-06-05 01:27:23.036 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:27:23.067 [info] > git rev-parse --show-toplevel [31ms]
2025-06-05 01:27:23.106 [info] > git config --get commit.template [34ms]
2025-06-05 01:27:23.106 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [50ms]
2025-06-05 01:27:23.113 [info] > git rev-parse --show-toplevel [34ms]
2025-06-05 01:27:23.117 [info] > git config --get --local branch.main.vscode-merge-base [7ms]
2025-06-05 01:27:23.125 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-05 01:27:23.126 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [5ms]
2025-06-05 01:27:23.132 [info] > git merge-base refs/heads/main refs/remotes/origin/main [2ms]
2025-06-05 01:27:23.136 [info] > git rev-parse --show-toplevel [11ms]
2025-06-05 01:27:23.142 [info] > git diff --name-status -z --diff-filter=ADMR 40f156400ee62158de598c268b329e23343defd1...refs/remotes/origin/main [7ms]
2025-06-05 01:27:23.143 [info] > git rev-parse --show-toplevel [3ms]
2025-06-05 01:27:23.155 [info] > git status -z -uall [7ms]
2025-06-05 01:27:23.156 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-05 01:27:23.162 [info] > git rev-parse --show-toplevel [8ms]
2025-06-05 01:27:23.199 [info] > git rev-parse --show-toplevel [33ms]
2025-06-05 01:27:23.205 [info] > git rev-parse --show-toplevel [3ms]
2025-06-05 01:27:23.211 [info] > git rev-parse --show-toplevel [2ms]
2025-06-05 01:27:23.218 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-05 01:27:23.503 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-05 01:27:27.091 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-05 01:27:27.091 [info] > git config --get commit.template [10ms]
2025-06-05 01:27:27.096 [info] > git status -z -uall [2ms]
2025-06-05 01:27:27.098 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:27:43.250 [info] > git config --get commit.template [3ms]
2025-06-05 01:27:43.251 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:27:43.257 [info] > git status -z -uall [3ms]
2025-06-05 01:27:43.261 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:27:48.271 [info] > git config --get commit.template [3ms]
2025-06-05 01:27:48.271 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:27:48.277 [info] > git status -z -uall [3ms]
2025-06-05 01:27:48.278 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:27:53.289 [info] > git config --get commit.template [3ms]
2025-06-05 01:27:53.289 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:27:53.296 [info] > git status -z -uall [4ms]
2025-06-05 01:27:53.297 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:27:58.306 [info] > git config --get commit.template [2ms]
2025-06-05 01:27:58.306 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:27:58.313 [info] > git status -z -uall [4ms]
2025-06-05 01:27:58.314 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:28:03.324 [info] > git config --get commit.template [2ms]
2025-06-05 01:28:03.324 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:28:03.330 [info] > git status -z -uall [3ms]
2025-06-05 01:28:03.331 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:28:19.475 [info] > git config --get commit.template [15ms]
2025-06-05 01:28:19.477 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-05 01:28:19.496 [info] > git status -z -uall [7ms]
2025-06-05 01:28:19.497 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:28:24.513 [info] > git config --get commit.template [6ms]
2025-06-05 01:28:24.514 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:28:24.540 [info] > git status -z -uall [13ms]
2025-06-05 01:28:24.541 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-05 01:28:29.553 [info] > git config --get commit.template [1ms]
2025-06-05 01:28:29.561 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:28:29.576 [info] > git status -z -uall [5ms]
2025-06-05 01:28:29.576 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:28:34.589 [info] > git config --get commit.template [3ms]
2025-06-05 01:28:34.590 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:28:34.600 [info] > git status -z -uall [5ms]
2025-06-05 01:28:34.600 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:28:39.616 [info] > git config --get commit.template [5ms]
2025-06-05 01:28:39.617 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:28:39.627 [info] > git status -z -uall [5ms]
2025-06-05 01:28:39.628 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:28:44.643 [info] > git config --get commit.template [5ms]
2025-06-05 01:28:44.644 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:28:44.653 [info] > git status -z -uall [4ms]
2025-06-05 01:28:44.654 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:28:49.668 [info] > git config --get commit.template [5ms]
2025-06-05 01:28:49.669 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:28:49.679 [info] > git status -z -uall [6ms]
2025-06-05 01:28:49.679 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:28:54.730 [info] > git config --get commit.template [9ms]
2025-06-05 01:28:54.731 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:28:54.748 [info] > git status -z -uall [9ms]
2025-06-05 01:28:54.748 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:28:59.764 [info] > git config --get commit.template [6ms]
2025-06-05 01:28:59.765 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:28:59.783 [info] > git status -z -uall [10ms]
2025-06-05 01:28:59.785 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:29:04.809 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:29:04.809 [info] > git config --get commit.template [8ms]
2025-06-05 01:29:04.827 [info] > git status -z -uall [11ms]
2025-06-05 01:29:04.832 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-05 01:29:09.842 [info] > git config --get commit.template [0ms]
2025-06-05 01:29:09.858 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-05 01:29:09.860 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-05 01:29:09.876 [info] > git status -z -uall [8ms]
2025-06-05 01:29:09.987 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [112ms]
2025-06-05 01:29:18.183 [info] > git config --get commit.template [4ms]
2025-06-05 01:29:18.184 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:29:18.194 [info] > git status -z -uall [6ms]
2025-06-05 01:29:18.195 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:29:23.208 [info] > git config --get commit.template [4ms]
2025-06-05 01:29:23.210 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:29:23.220 [info] > git status -z -uall [6ms]
2025-06-05 01:29:23.221 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:29:28.241 [info] > git config --get commit.template [10ms]
2025-06-05 01:29:28.243 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:29:28.255 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:29:28.260 [info] > git status -z -uall [10ms]
2025-06-05 01:29:33.274 [info] > git config --get commit.template [5ms]
2025-06-05 01:29:33.275 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:29:33.284 [info] > git status -z -uall [5ms]
2025-06-05 01:29:33.285 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:29:38.293 [info] > git config --get commit.template [1ms]
2025-06-05 01:29:38.302 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:29:38.337 [info] > git status -z -uall [11ms]
2025-06-05 01:29:38.338 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:29:43.352 [info] > git config --get commit.template [6ms]
2025-06-05 01:29:43.353 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:29:43.362 [info] > git status -z -uall [4ms]
2025-06-05 01:29:43.362 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:29:48.378 [info] > git config --get commit.template [7ms]
2025-06-05 01:29:48.379 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:29:48.387 [info] > git status -z -uall [4ms]
2025-06-05 01:29:48.389 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:29:53.423 [info] > git config --get commit.template [1ms]
2025-06-05 01:29:53.439 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:29:53.459 [info] > git status -z -uall [8ms]
2025-06-05 01:29:53.462 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:29:55.941 [info] > git log --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z --shortstat --diff-merges=first-parent -n50 --skip=0 --topo-order --decorate=full --stdin [266ms]
2025-06-05 01:29:57.466 [info] > git log --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z --shortstat --diff-merges=first-parent -n50 --skip=0 --topo-order --decorate=full --stdin [66ms]
2025-06-05 01:29:58.480 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-05 01:29:58.481 [info] > git config --get commit.template [10ms]
2025-06-05 01:29:58.494 [info] > git status -z -uall [5ms]
2025-06-05 01:29:58.497 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:30:03.515 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:30:03.515 [info] > git config --get commit.template [6ms]
2025-06-05 01:30:03.522 [info] > git status -z -uall [3ms]
2025-06-05 01:30:03.523 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:32:10.505 [info] > git config --get commit.template [1ms]
2025-06-05 01:32:10.537 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:32:10.592 [info] > git status -z -uall [31ms]
2025-06-05 01:32:10.594 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:32:15.621 [info] > git config --get commit.template [8ms]
2025-06-05 01:32:15.625 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-05 01:32:15.643 [info] > git status -z -uall [6ms]
2025-06-05 01:32:15.646 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:32:20.670 [info] > git config --get commit.template [5ms]
2025-06-05 01:32:20.686 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:32:20.717 [info] > git status -z -uall [10ms]
2025-06-05 01:32:20.736 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [21ms]
2025-06-05 01:32:52.924 [info] > git config --get commit.template [0ms]
2025-06-05 01:32:52.935 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:32:52.955 [info] > git status -z -uall [8ms]
2025-06-05 01:32:52.960 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-05 01:32:58.056 [info] > git config --get commit.template [10ms]
2025-06-05 01:32:58.064 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [12ms]
2025-06-05 01:32:58.086 [info] > git status -z -uall [9ms]
2025-06-05 01:32:58.089 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:33:03.103 [info] > git config --get commit.template [1ms]
2025-06-05 01:33:03.111 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:33:03.133 [info] > git status -z -uall [6ms]
2025-06-05 01:33:03.133 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:33:08.159 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:33:08.159 [info] > git config --get commit.template [13ms]
2025-06-05 01:33:08.176 [info] > git status -z -uall [9ms]
2025-06-05 01:33:08.177 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:33:13.198 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:33:13.199 [info] > git config --get commit.template [9ms]
2025-06-05 01:33:13.214 [info] > git status -z -uall [9ms]
2025-06-05 01:33:13.217 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:33:18.230 [info] > git config --get commit.template [0ms]
2025-06-05 01:33:18.246 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:33:18.257 [info] > git status -z -uall [6ms]
2025-06-05 01:33:18.260 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:33:23.278 [info] > git config --get commit.template [9ms]
2025-06-05 01:33:23.280 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:33:23.304 [info] > git status -z -uall [15ms]
2025-06-05 01:33:23.304 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:33:28.322 [info] > git config --get commit.template [0ms]
2025-06-05 01:33:28.330 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:33:28.345 [info] > git status -z -uall [9ms]
2025-06-05 01:33:28.346 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:33:33.373 [info] > git config --get commit.template [12ms]
2025-06-05 01:33:33.386 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:33:33.412 [info] > git status -z -uall [17ms]
2025-06-05 01:33:33.413 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:33:38.433 [info] > git config --get commit.template [9ms]
2025-06-05 01:33:38.443 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-05 01:33:38.466 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:33:38.480 [info] > git status -z -uall [25ms]
2025-06-05 01:33:43.507 [info] > git config --get commit.template [11ms]
2025-06-05 01:33:43.508 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:33:43.518 [info] > git status -z -uall [4ms]
2025-06-05 01:33:43.523 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:33:48.536 [info] > git config --get commit.template [2ms]
2025-06-05 01:33:48.545 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:33:48.580 [info] > git status -z -uall [15ms]
2025-06-05 01:33:48.582 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:33:53.611 [info] > git config --get commit.template [18ms]
2025-06-05 01:33:53.616 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-05 01:33:53.659 [info] > git status -z -uall [26ms]
2025-06-05 01:33:53.659 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:33:58.679 [info] > git config --get commit.template [3ms]
2025-06-05 01:33:58.690 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:33:58.712 [info] > git status -z -uall [12ms]
2025-06-05 01:33:58.713 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:34:03.730 [info] > git config --get commit.template [1ms]
2025-06-05 01:34:03.740 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:34:03.767 [info] > git status -z -uall [18ms]
2025-06-05 01:34:03.768 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-05 01:34:08.783 [info] > git config --get commit.template [2ms]
2025-06-05 01:34:08.792 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:34:08.807 [info] > git status -z -uall [6ms]
2025-06-05 01:34:08.809 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:34:13.823 [info] > git config --get commit.template [3ms]
2025-06-05 01:34:13.840 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-05 01:34:13.861 [info] > git status -z -uall [11ms]
2025-06-05 01:34:13.862 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:34:18.885 [info] > git config --get commit.template [6ms]
2025-06-05 01:34:18.886 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:34:18.904 [info] > git status -z -uall [7ms]
2025-06-05 01:34:18.906 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:34:23.922 [info] > git config --get commit.template [0ms]
2025-06-05 01:34:23.937 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:34:23.951 [info] > git status -z -uall [8ms]
2025-06-05 01:34:23.953 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:34:28.967 [info] > git config --get commit.template [2ms]
2025-06-05 01:34:28.978 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:34:29.002 [info] > git status -z -uall [17ms]
2025-06-05 01:34:29.005 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-05 01:34:37.070 [info] > git config --get commit.template [6ms]
2025-06-05 01:34:37.072 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 01:34:37.088 [info] > git status -z -uall [10ms]
2025-06-05 01:34:37.091 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-05 01:34:42.114 [info] > git config --get commit.template [10ms]
2025-06-05 01:34:42.115 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:34:42.130 [info] > git status -z -uall [8ms]
2025-06-05 01:34:42.131 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:34:47.150 [info] > git config --get commit.template [10ms]
2025-06-05 01:34:47.153 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:34:47.170 [info] > git status -z -uall [10ms]
2025-06-05 01:34:47.171 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:34:52.188 [info] > git config --get commit.template [6ms]
2025-06-05 01:34:52.188 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:34:52.198 [info] > git status -z -uall [6ms]
2025-06-05 01:34:52.199 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:34:57.218 [info] > git config --get commit.template [10ms]
2025-06-05 01:34:57.222 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:34:57.231 [info] > git status -z -uall [4ms]
2025-06-05 01:34:57.233 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:35:02.247 [info] > git config --get commit.template [6ms]
2025-06-05 01:35:02.248 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:35:02.259 [info] > git status -z -uall [6ms]
2025-06-05 01:35:02.260 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
