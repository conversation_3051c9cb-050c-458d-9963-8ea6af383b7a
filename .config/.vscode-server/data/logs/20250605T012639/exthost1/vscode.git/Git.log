2025-06-05 01:26:48.304 [info] [main] Log level: Info
2025-06-05 01:26:48.304 [info] [main] Validating found git in: "git"
2025-06-05 01:26:48.304 [info] [main] Using git "2.47.2" from "git"
2025-06-05 01:26:48.304 [info] [Model][doInitialScan] Initial repository scan started
2025-06-05 01:26:48.304 [info] > git rev-parse --show-toplevel [3ms]
2025-06-05 01:26:48.304 [info] > git rev-parse --git-dir --git-common-dir [1ms]
2025-06-05 01:26:48.304 [info] [Model][openRepository] Opened repository: /home/<USER>/workspace
2025-06-05 01:26:48.304 [info] > git config --get commit.template [4ms]
2025-06-05 01:26:48.304 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:26:48.304 [info] > git rev-parse --show-toplevel [1ms]
2025-06-05 01:26:48.304 [info] > git status -z -uall [4ms]
2025-06-05 01:26:48.304 [info] > git rev-parse --show-toplevel [6ms]
2025-06-05 01:26:48.304 [info] > git for-each-ref --sort -committerdate --format %(refname) %(objectname) %(*objectname) [2ms]
2025-06-05 01:26:48.304 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-05 01:26:48.304 [info] > git config --get commit.template [4ms]
2025-06-05 01:26:48.304 [info] > git rev-parse --show-toplevel [7ms]
2025-06-05 01:26:48.304 [info] > git config --local branch.main.vscode-merge-base [5ms]
2025-06-05 01:26:48.304 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-05 01:26:48.305 [info] > git rev-parse --show-toplevel [9ms]
2025-06-05 01:26:48.305 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [11ms]
2025-06-05 01:26:48.305 [info] > git merge-base refs/heads/main refs/remotes/origin/main [11ms]
2025-06-05 01:26:48.305 [info] > git rev-parse --show-toplevel [22ms]
2025-06-05 01:26:48.305 [info] > git diff --name-status -z --diff-filter=ADMR 40f156400ee62158de598c268b329e23343defd1...refs/remotes/origin/main [24ms]
2025-06-05 01:26:48.309 [info] > git rev-parse --show-toplevel [8ms]
2025-06-05 01:26:48.312 [info] > git status -z -uall [5ms]
2025-06-05 01:26:48.313 [info] > git rev-parse --show-toplevel [2ms]
2025-06-05 01:26:48.313 [info] > git for-each-ref --sort -committerdate --format %(refname) %(objectname) %(*objectname) [4ms]
2025-06-05 01:26:48.319 [info] > git rev-parse --show-toplevel [1ms]
2025-06-05 01:26:48.324 [info] > git rev-parse --show-toplevel [2ms]
2025-06-05 01:26:48.327 [info] > git rev-parse --show-toplevel [1ms]
2025-06-05 01:26:48.331 [info] > git rev-parse --show-toplevel [1ms]
2025-06-05 01:26:48.334 [info] > git rev-parse --show-toplevel [1ms]
2025-06-05 01:26:48.337 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-05 01:26:48.766 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-05 01:26:51.022 [info] > git config --get commit.template [4ms]
2025-06-05 01:26:51.022 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:26:51.028 [info] > git status -z -uall [3ms]
2025-06-05 01:26:51.030 [info] > git for-each-ref --sort -committerdate --format %(refname) %(objectname) %(*objectname) [2ms]
2025-06-05 01:26:51.610 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-05 01:26:56.039 [info] > git config --get commit.template [3ms]
2025-06-05 01:26:56.040 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:26:56.048 [info] > git status -z -uall [5ms]
2025-06-05 01:26:56.049 [info] > git for-each-ref --sort -committerdate --format %(refname) %(objectname) %(*objectname) [2ms]
