2025-06-05 01:56:22.446 [info] [main] Log level: Info
2025-06-05 01:56:22.446 [info] [main] Validating found git in: "git"
2025-06-05 01:56:22.446 [info] [main] Using git "2.47.2" from "git"
2025-06-05 01:56:22.446 [info] [Model][doInitialScan] Initial repository scan started
2025-06-05 01:56:22.446 [info] > git rev-parse --show-toplevel [28ms]
2025-06-05 01:56:22.446 [info] > git rev-parse --git-dir --git-common-dir [22ms]
2025-06-05 01:56:22.446 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-05 01:56:22.446 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-05 01:56:22.446 [info] > git rev-parse --show-toplevel [56ms]
2025-06-05 01:56:22.446 [info] > git config --get commit.template [178ms]
2025-06-05 01:56:22.446 [info] > git rev-parse --show-toplevel [8ms]
2025-06-05 01:56:22.450 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [31ms]
2025-06-05 01:56:23.352 [info] > git check-ignore -v -z --stdin [56ms]
2025-06-05 01:56:23.374 [info] > git rev-parse --show-toplevel [873ms]
2025-06-05 01:56:23.554 [info] > git status -z -uall [7ms]
2025-06-05 01:56:23.554 [info] > git rev-parse --show-toplevel [77ms]
2025-06-05 01:56:23.557 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:56:23.595 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-06-05 01:56:23.602 [info] > git rev-parse --show-toplevel [18ms]
2025-06-05 01:56:23.607 [info] > git config --get --local branch.main.vscode-merge-base [8ms]
2025-06-05 01:56:23.628 [info] > git config --get commit.template [34ms]
2025-06-05 01:56:23.629 [info] > git rev-parse --show-toplevel [9ms]
2025-06-05 01:56:23.629 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [16ms]
2025-06-05 01:56:23.660 [info] > git merge-base refs/heads/main refs/remotes/origin/main [17ms]
2025-06-05 01:56:23.675 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [50ms]
2025-06-05 01:56:23.680 [info] > git diff --name-status -z --diff-filter=ADMR 40f156400ee62158de598c268b329e23343defd1...refs/remotes/origin/main [5ms]
2025-06-05 01:56:23.681 [info] > git rev-parse --show-toplevel [26ms]
2025-06-05 01:56:23.735 [info] > git status -z -uall [19ms]
2025-06-05 01:56:23.735 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:56:23.744 [info] > git rev-parse --show-toplevel [57ms]
2025-06-05 01:56:23.854 [info] > git rev-parse --show-toplevel [98ms]
2025-06-05 01:56:23.863 [info] > git rev-parse --show-toplevel [2ms]
2025-06-05 01:56:23.889 [info] > git rev-parse --show-toplevel [9ms]
2025-06-05 01:56:24.037 [info] > git rev-parse --show-toplevel [138ms]
2025-06-05 01:56:24.040 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-05 01:56:24.058 [info] > git show --textconv :test-full-conversation.js [13ms]
2025-06-05 01:56:24.062 [info] > git ls-files --stage -- test-full-conversation.js [12ms]
2025-06-05 01:56:24.092 [info] > git hash-object -t tree /dev/null [31ms]
2025-06-05 01:56:24.092 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/workspace/test-full-conversation.js.git?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2Ftest-full-conversation.js%22%2C%22ref%22%3A%22%22%7D
2025-06-05 01:56:24.527 [info] > git hash-object -t tree /dev/null [439ms]
2025-06-05 01:56:24.527 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/workspace/test-full-conversation.js.git?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2Ftest-full-conversation.js%22%2C%22ref%22%3A%22%22%7D
2025-06-05 01:56:25.090 [info] > git blame --root --incremental 40f156400ee62158de598c268b329e23343defd1 -- test-full-conversation.js [2ms]
2025-06-05 01:56:25.090 [info] fatal: no such path test-full-conversation.js in 40f156400ee62158de598c268b329e23343defd1
2025-06-05 01:56:37.668 [info] > git config --get commit.template [4ms]
2025-06-05 01:56:37.671 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:56:37.680 [info] > git status -z -uall [5ms]
2025-06-05 01:56:37.681 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:56:42.727 [info] > git config --get commit.template [4ms]
2025-06-05 01:56:42.728 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:56:42.738 [info] > git status -z -uall [4ms]
2025-06-05 01:56:42.739 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:56:47.749 [info] > git config --get commit.template [3ms]
2025-06-05 01:56:47.750 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:56:47.759 [info] > git status -z -uall [5ms]
2025-06-05 01:56:47.760 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:56:52.773 [info] > git config --get commit.template [6ms]
2025-06-05 01:56:52.774 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:56:52.781 [info] > git status -z -uall [3ms]
2025-06-05 01:56:52.782 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:56:57.793 [info] > git config --get commit.template [5ms]
2025-06-05 01:56:57.794 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:56:57.801 [info] > git status -z -uall [4ms]
2025-06-05 01:56:57.803 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:57:10.180 [info] > git config --get commit.template [3ms]
2025-06-05 01:57:10.181 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:57:10.191 [info] > git status -z -uall [6ms]
2025-06-05 01:57:10.192 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:57:15.233 [info] > git config --get commit.template [24ms]
2025-06-05 01:57:15.240 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-05 01:57:15.259 [info] > git status -z -uall [7ms]
2025-06-05 01:57:15.261 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:57:20.271 [info] > git config --get commit.template [1ms]
2025-06-05 01:57:20.277 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:57:20.286 [info] > git status -z -uall [3ms]
2025-06-05 01:57:20.287 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
