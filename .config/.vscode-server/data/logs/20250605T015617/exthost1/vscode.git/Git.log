2025-06-05 01:56:22.446 [info] [main] Log level: Info
2025-06-05 01:56:22.446 [info] [main] Validating found git in: "git"
2025-06-05 01:56:22.446 [info] [main] Using git "2.47.2" from "git"
2025-06-05 01:56:22.446 [info] [Model][doInitialScan] Initial repository scan started
2025-06-05 01:56:22.446 [info] > git rev-parse --show-toplevel [28ms]
2025-06-05 01:56:22.446 [info] > git rev-parse --git-dir --git-common-dir [22ms]
2025-06-05 01:56:22.446 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-05 01:56:22.446 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-05 01:56:22.446 [info] > git rev-parse --show-toplevel [56ms]
2025-06-05 01:56:22.446 [info] > git config --get commit.template [178ms]
2025-06-05 01:56:22.446 [info] > git rev-parse --show-toplevel [8ms]
2025-06-05 01:56:22.450 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [31ms]
2025-06-05 01:56:23.352 [info] > git check-ignore -v -z --stdin [56ms]
2025-06-05 01:56:23.374 [info] > git rev-parse --show-toplevel [873ms]
2025-06-05 01:56:23.554 [info] > git status -z -uall [7ms]
2025-06-05 01:56:23.554 [info] > git rev-parse --show-toplevel [77ms]
2025-06-05 01:56:23.557 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:56:23.595 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-06-05 01:56:23.602 [info] > git rev-parse --show-toplevel [18ms]
2025-06-05 01:56:23.607 [info] > git config --get --local branch.main.vscode-merge-base [8ms]
2025-06-05 01:56:23.628 [info] > git config --get commit.template [34ms]
2025-06-05 01:56:23.629 [info] > git rev-parse --show-toplevel [9ms]
2025-06-05 01:56:23.629 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [16ms]
2025-06-05 01:56:23.660 [info] > git merge-base refs/heads/main refs/remotes/origin/main [17ms]
2025-06-05 01:56:23.675 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [50ms]
2025-06-05 01:56:23.680 [info] > git diff --name-status -z --diff-filter=ADMR 40f156400ee62158de598c268b329e23343defd1...refs/remotes/origin/main [5ms]
2025-06-05 01:56:23.681 [info] > git rev-parse --show-toplevel [26ms]
2025-06-05 01:56:23.735 [info] > git status -z -uall [19ms]
2025-06-05 01:56:23.735 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:56:23.744 [info] > git rev-parse --show-toplevel [57ms]
2025-06-05 01:56:23.854 [info] > git rev-parse --show-toplevel [98ms]
2025-06-05 01:56:23.863 [info] > git rev-parse --show-toplevel [2ms]
2025-06-05 01:56:23.889 [info] > git rev-parse --show-toplevel [9ms]
2025-06-05 01:56:24.037 [info] > git rev-parse --show-toplevel [138ms]
2025-06-05 01:56:24.040 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-05 01:56:24.058 [info] > git show --textconv :test-full-conversation.js [13ms]
2025-06-05 01:56:24.062 [info] > git ls-files --stage -- test-full-conversation.js [12ms]
2025-06-05 01:56:24.092 [info] > git hash-object -t tree /dev/null [31ms]
2025-06-05 01:56:24.092 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/workspace/test-full-conversation.js.git?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2Ftest-full-conversation.js%22%2C%22ref%22%3A%22%22%7D
2025-06-05 01:56:24.527 [info] > git hash-object -t tree /dev/null [439ms]
2025-06-05 01:56:24.527 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/workspace/test-full-conversation.js.git?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2Ftest-full-conversation.js%22%2C%22ref%22%3A%22%22%7D
2025-06-05 01:56:25.090 [info] > git blame --root --incremental 40f156400ee62158de598c268b329e23343defd1 -- test-full-conversation.js [2ms]
2025-06-05 01:56:25.090 [info] fatal: no such path test-full-conversation.js in 40f156400ee62158de598c268b329e23343defd1
2025-06-05 01:56:37.668 [info] > git config --get commit.template [4ms]
2025-06-05 01:56:37.671 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:56:37.680 [info] > git status -z -uall [5ms]
2025-06-05 01:56:37.681 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:56:42.727 [info] > git config --get commit.template [4ms]
2025-06-05 01:56:42.728 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:56:42.738 [info] > git status -z -uall [4ms]
2025-06-05 01:56:42.739 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:56:47.749 [info] > git config --get commit.template [3ms]
2025-06-05 01:56:47.750 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:56:47.759 [info] > git status -z -uall [5ms]
2025-06-05 01:56:47.760 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:56:52.773 [info] > git config --get commit.template [6ms]
2025-06-05 01:56:52.774 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:56:52.781 [info] > git status -z -uall [3ms]
2025-06-05 01:56:52.782 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:56:57.793 [info] > git config --get commit.template [5ms]
2025-06-05 01:56:57.794 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:56:57.801 [info] > git status -z -uall [4ms]
2025-06-05 01:56:57.803 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:57:10.180 [info] > git config --get commit.template [3ms]
2025-06-05 01:57:10.181 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:57:10.191 [info] > git status -z -uall [6ms]
2025-06-05 01:57:10.192 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:57:15.233 [info] > git config --get commit.template [24ms]
2025-06-05 01:57:15.240 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-05 01:57:15.259 [info] > git status -z -uall [7ms]
2025-06-05 01:57:15.261 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:57:20.271 [info] > git config --get commit.template [1ms]
2025-06-05 01:57:20.277 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:57:20.286 [info] > git status -z -uall [3ms]
2025-06-05 01:57:20.287 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:57:25.297 [info] > git config --get commit.template [4ms]
2025-06-05 01:57:25.298 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:57:25.307 [info] > git status -z -uall [4ms]
2025-06-05 01:57:25.308 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:57:30.326 [info] > git config --get commit.template [7ms]
2025-06-05 01:57:30.327 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:57:30.342 [info] > git status -z -uall [7ms]
2025-06-05 01:57:30.344 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:57:35.357 [info] > git config --get commit.template [4ms]
2025-06-05 01:57:35.358 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:57:35.369 [info] > git status -z -uall [6ms]
2025-06-05 01:57:35.371 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:58:25.186 [info] > git config --get commit.template [4ms]
2025-06-05 01:58:25.186 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:58:25.196 [info] > git status -z -uall [5ms]
2025-06-05 01:58:25.196 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:58:30.210 [info] > git config --get commit.template [6ms]
2025-06-05 01:58:30.211 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:58:30.219 [info] > git status -z -uall [4ms]
2025-06-05 01:58:30.221 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:58:35.232 [info] > git config --get commit.template [4ms]
2025-06-05 01:58:35.233 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:58:35.241 [info] > git status -z -uall [5ms]
2025-06-05 01:58:35.242 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:58:40.255 [info] > git config --get commit.template [5ms]
2025-06-05 01:58:40.256 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:58:40.264 [info] > git status -z -uall [5ms]
2025-06-05 01:58:40.265 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:58:48.676 [info] > git config --get commit.template [3ms]
2025-06-05 01:58:48.677 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:58:48.686 [info] > git status -z -uall [4ms]
2025-06-05 01:58:48.687 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:58:53.701 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-05 01:58:53.701 [info] > git config --get commit.template [7ms]
2025-06-05 01:58:53.710 [info] > git status -z -uall [5ms]
2025-06-05 01:58:53.711 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:58:58.725 [info] > git config --get commit.template [4ms]
2025-06-05 01:58:58.726 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:58:58.735 [info] > git status -z -uall [4ms]
2025-06-05 01:58:58.737 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:59:03.786 [info] > git config --get commit.template [6ms]
2025-06-05 01:59:03.787 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:59:03.799 [info] > git status -z -uall [8ms]
2025-06-05 01:59:03.800 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 01:59:08.826 [info] > git config --get commit.template [6ms]
2025-06-05 01:59:08.840 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:59:08.874 [info] > git status -z -uall [14ms]
2025-06-05 01:59:08.875 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:59:13.889 [info] > git config --get commit.template [5ms]
2025-06-05 01:59:13.890 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:59:13.901 [info] > git status -z -uall [5ms]
2025-06-05 01:59:13.902 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:59:18.916 [info] > git config --get commit.template [6ms]
2025-06-05 01:59:18.917 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:59:18.929 [info] > git status -z -uall [6ms]
2025-06-05 01:59:18.931 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:59:23.943 [info] > git config --get commit.template [4ms]
2025-06-05 01:59:23.944 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:59:23.953 [info] > git status -z -uall [4ms]
2025-06-05 01:59:23.954 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:59:28.966 [info] > git config --get commit.template [5ms]
2025-06-05 01:59:28.967 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:59:28.981 [info] > git status -z -uall [8ms]
2025-06-05 01:59:28.981 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 01:59:33.996 [info] > git config --get commit.template [5ms]
2025-06-05 01:59:33.997 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:59:34.013 [info] > git status -z -uall [8ms]
2025-06-05 01:59:34.015 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 01:59:39.031 [info] > git config --get commit.template [6ms]
2025-06-05 01:59:39.034 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 01:59:39.050 [info] > git status -z -uall [10ms]
2025-06-05 01:59:39.051 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 01:59:44.068 [info] > git config --get commit.template [6ms]
2025-06-05 01:59:44.068 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 01:59:44.085 [info] > git status -z -uall [6ms]
2025-06-05 01:59:44.087 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 01:59:49.104 [info] > git config --get commit.template [6ms]
2025-06-05 01:59:49.105 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 01:59:49.117 [info] > git status -z -uall [4ms]
2025-06-05 01:59:49.118 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 02:00:06.651 [info] > git config --get commit.template [10ms]
2025-06-05 02:00:06.654 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 02:00:06.685 [info] > git status -z -uall [11ms]
2025-06-05 02:00:06.690 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 02:00:11.720 [info] > git config --get commit.template [18ms]
2025-06-05 02:00:11.724 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 02:00:11.744 [info] > git status -z -uall [10ms]
2025-06-05 02:00:11.749 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-05 02:00:16.772 [info] > git config --get commit.template [8ms]
2025-06-05 02:00:16.774 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:00:16.803 [info] > git status -z -uall [16ms]
2025-06-05 02:00:16.805 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:00:21.820 [info] > git config --get commit.template [3ms]
2025-06-05 02:00:21.827 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 02:00:21.841 [info] > git status -z -uall [7ms]
2025-06-05 02:00:21.842 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 02:00:26.865 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:00:26.866 [info] > git config --get commit.template [13ms]
2025-06-05 02:00:26.898 [info] > git status -z -uall [13ms]
2025-06-05 02:00:26.899 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:00:31.914 [info] > git config --get commit.template [7ms]
2025-06-05 02:00:31.926 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:00:31.953 [info] > git status -z -uall [18ms]
2025-06-05 02:00:31.954 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-05 02:00:36.966 [info] > git config --get commit.template [4ms]
2025-06-05 02:00:36.967 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:00:36.976 [info] > git status -z -uall [5ms]
2025-06-05 02:00:36.976 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 02:00:41.988 [info] > git config --get commit.template [5ms]
2025-06-05 02:00:41.989 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:00:42.000 [info] > git status -z -uall [5ms]
2025-06-05 02:00:42.001 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:00:47.012 [info] > git config --get commit.template [1ms]
2025-06-05 02:00:47.019 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:00:47.028 [info] > git status -z -uall [4ms]
2025-06-05 02:00:47.029 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 02:00:52.055 [info] > git config --get commit.template [1ms]
2025-06-05 02:00:52.069 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 02:00:52.086 [info] > git status -z -uall [7ms]
2025-06-05 02:00:52.087 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:00:58.900 [info] > git config --get commit.template [5ms]
2025-06-05 02:00:58.901 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:00:58.916 [info] > git status -z -uall [7ms]
2025-06-05 02:00:58.917 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 02:01:03.955 [info] > git config --get commit.template [12ms]
2025-06-05 02:01:03.956 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:01:03.972 [info] > git status -z -uall [7ms]
2025-06-05 02:01:03.974 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 02:02:57.940 [info] > git config --get commit.template [5ms]
2025-06-05 02:02:57.941 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:02:57.956 [info] > git status -z -uall [8ms]
2025-06-05 02:02:57.956 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 02:03:02.966 [info] > git config --get commit.template [1ms]
2025-06-05 02:03:02.981 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:03:02.992 [info] > git status -z -uall [6ms]
2025-06-05 02:03:02.993 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:03:08.011 [info] > git config --get commit.template [7ms]
2025-06-05 02:03:08.016 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-05 02:03:08.053 [info] > git status -z -uall [19ms]
2025-06-05 02:03:08.059 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-05 02:03:13.075 [info] > git config --get commit.template [6ms]
2025-06-05 02:03:13.077 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:03:13.094 [info] > git status -z -uall [10ms]
2025-06-05 02:03:13.096 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:03:18.105 [info] > git config --get commit.template [1ms]
2025-06-05 02:03:18.112 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:03:18.121 [info] > git status -z -uall [4ms]
2025-06-05 02:03:18.122 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 02:03:23.132 [info] > git config --get commit.template [1ms]
2025-06-05 02:03:23.139 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:03:23.155 [info] > git status -z -uall [7ms]
2025-06-05 02:03:23.156 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:03:28.165 [info] > git config --get commit.template [3ms]
2025-06-05 02:03:28.166 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:03:28.173 [info] > git status -z -uall [4ms]
2025-06-05 02:03:28.174 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:03:33.188 [info] > git config --get commit.template [7ms]
2025-06-05 02:03:33.189 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:03:33.199 [info] > git status -z -uall [4ms]
2025-06-05 02:03:33.199 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 02:03:38.218 [info] > git config --get commit.template [9ms]
2025-06-05 02:03:38.218 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:03:38.228 [info] > git status -z -uall [5ms]
2025-06-05 02:03:38.229 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 02:03:43.242 [info] > git config --get commit.template [0ms]
2025-06-05 02:03:43.248 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:03:43.256 [info] > git status -z -uall [4ms]
2025-06-05 02:03:43.256 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 02:03:48.265 [info] > git config --get commit.template [2ms]
2025-06-05 02:03:48.270 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:03:48.278 [info] > git status -z -uall [4ms]
2025-06-05 02:03:48.279 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:03:53.312 [info] > git config --get commit.template [6ms]
2025-06-05 02:03:53.314 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:03:53.325 [info] > git status -z -uall [6ms]
2025-06-05 02:03:53.325 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [0ms]
2025-06-05 02:03:58.336 [info] > git config --get commit.template [4ms]
2025-06-05 02:03:58.337 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:03:58.345 [info] > git status -z -uall [4ms]
2025-06-05 02:03:58.346 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:04:03.357 [info] > git config --get commit.template [1ms]
2025-06-05 02:04:03.364 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:04:03.382 [info] > git status -z -uall [11ms]
2025-06-05 02:04:03.383 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:04:08.398 [info] > git config --get commit.template [6ms]
2025-06-05 02:04:08.399 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:04:08.414 [info] > git status -z -uall [8ms]
2025-06-05 02:04:08.417 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 02:05:24.045 [info] > git config --get commit.template [5ms]
2025-06-05 02:05:24.046 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:05:24.060 [info] > git status -z -uall [6ms]
2025-06-05 02:05:24.061 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:05:29.091 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:05:29.092 [info] > git config --get commit.template [19ms]
2025-06-05 02:05:29.116 [info] > git status -z -uall [10ms]
2025-06-05 02:05:29.121 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-05 02:05:34.529 [info] > git config --get commit.template [383ms]
2025-06-05 02:05:34.602 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [25ms]
2025-06-05 02:05:34.782 [info] > git status -z -uall [157ms]
2025-06-05 02:05:34.783 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [140ms]
2025-06-05 02:05:39.806 [info] > git config --get commit.template [10ms]
2025-06-05 02:05:39.809 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 02:05:39.825 [info] > git status -z -uall [7ms]
2025-06-05 02:05:39.827 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 02:05:44.843 [info] > git config --get commit.template [0ms]
2025-06-05 02:05:44.853 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-05 02:05:44.870 [info] > git status -z -uall [12ms]
2025-06-05 02:05:44.871 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:05:50.022 [info] > git config --get commit.template [135ms]
2025-06-05 02:05:50.067 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:05:50.087 [info] > git status -z -uall [13ms]
2025-06-05 02:05:50.089 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-05 02:05:55.171 [info] > git config --get commit.template [60ms]
2025-06-05 02:05:55.181 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:05:55.197 [info] > git status -z -uall [6ms]
2025-06-05 02:05:55.198 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:06:00.211 [info] > git config --get commit.template [5ms]
2025-06-05 02:06:00.212 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:06:00.219 [info] > git status -z -uall [3ms]
2025-06-05 02:06:00.221 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 02:06:05.230 [info] > git config --get commit.template [1ms]
2025-06-05 02:06:05.237 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:06:05.251 [info] > git status -z -uall [9ms]
2025-06-05 02:06:05.252 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:06:10.269 [info] > git config --get commit.template [1ms]
2025-06-05 02:06:10.276 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:06:10.294 [info] > git status -z -uall [14ms]
2025-06-05 02:06:10.295 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-05 02:06:15.311 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-05 02:06:15.312 [info] > git config --get commit.template [8ms]
2025-06-05 02:06:15.322 [info] > git status -z -uall [5ms]
2025-06-05 02:06:15.323 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:06:20.338 [info] > git config --get commit.template [6ms]
2025-06-05 02:06:20.339 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:06:20.347 [info] > git status -z -uall [4ms]
2025-06-05 02:06:20.349 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 02:06:25.419 [info] > git config --get commit.template [7ms]
2025-06-05 02:06:25.419 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:06:25.435 [info] > git status -z -uall [9ms]
2025-06-05 02:06:25.440 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-05 02:06:30.452 [info] > git config --get commit.template [2ms]
2025-06-05 02:06:30.469 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:06:30.491 [info] > git status -z -uall [12ms]
2025-06-05 02:06:30.492 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:06:35.502 [info] > git config --get commit.template [2ms]
2025-06-05 02:06:35.508 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:06:35.518 [info] > git status -z -uall [4ms]
2025-06-05 02:06:35.518 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 02:06:40.532 [info] > git config --get commit.template [4ms]
2025-06-05 02:06:40.533 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:06:40.543 [info] > git status -z -uall [6ms]
2025-06-05 02:06:40.544 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:06:45.558 [info] > git config --get commit.template [2ms]
2025-06-05 02:06:45.566 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:06:45.581 [info] > git status -z -uall [8ms]
2025-06-05 02:06:45.581 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 02:06:50.597 [info] > git config --get commit.template [6ms]
2025-06-05 02:06:50.599 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:06:50.615 [info] > git status -z -uall [8ms]
2025-06-05 02:06:50.617 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:06:55.647 [info] > git config --get commit.template [3ms]
2025-06-05 02:06:55.673 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-05 02:06:55.805 [info] > git status -z -uall [88ms]
2025-06-05 02:06:55.807 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-05 02:07:00.825 [info] > git config --get commit.template [7ms]
2025-06-05 02:07:00.828 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 02:07:00.839 [info] > git status -z -uall [5ms]
2025-06-05 02:07:00.840 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 02:07:05.852 [info] > git config --get commit.template [5ms]
2025-06-05 02:07:05.853 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:07:05.862 [info] > git status -z -uall [5ms]
2025-06-05 02:07:05.863 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:08:17.670 [info] > git config --get commit.template [3ms]
2025-06-05 02:08:17.672 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:08:17.679 [info] > git status -z -uall [4ms]
2025-06-05 02:08:17.680 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 02:08:22.692 [info] > git config --get commit.template [4ms]
2025-06-05 02:08:22.693 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:08:22.700 [info] > git status -z -uall [4ms]
2025-06-05 02:08:22.701 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 02:08:27.712 [info] > git config --get commit.template [4ms]
2025-06-05 02:08:27.713 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:08:27.720 [info] > git status -z -uall [4ms]
2025-06-05 02:08:27.721 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:09:33.410 [info] > git config --get commit.template [8ms]
2025-06-05 02:09:33.411 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:09:33.423 [info] > git status -z -uall [5ms]
2025-06-05 02:09:33.425 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:09:38.446 [info] > git config --get commit.template [11ms]
2025-06-05 02:09:38.447 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:09:38.465 [info] > git status -z -uall [8ms]
2025-06-05 02:09:38.466 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:09:43.477 [info] > git config --get commit.template [1ms]
2025-06-05 02:09:43.484 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:09:43.493 [info] > git status -z -uall [5ms]
2025-06-05 02:09:43.495 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 02:10:23.642 [info] > git config --get commit.template [8ms]
2025-06-05 02:10:23.645 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 02:10:23.677 [info] > git status -z -uall [15ms]
2025-06-05 02:10:23.677 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 02:11:42.354 [info] > git config --get commit.template [8ms]
2025-06-05 02:11:42.355 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:11:42.374 [info] > git status -z -uall [10ms]
2025-06-05 02:11:42.375 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 02:11:47.419 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-05 02:11:47.419 [info] > git config --get commit.template [27ms]
2025-06-05 02:11:47.448 [info] > git status -z -uall [17ms]
2025-06-05 02:11:47.449 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:11:52.463 [info] > git config --get commit.template [5ms]
2025-06-05 02:11:52.464 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:11:52.481 [info] > git status -z -uall [8ms]
2025-06-05 02:11:52.483 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:11:57.512 [info] > git config --get commit.template [11ms]
2025-06-05 02:11:57.514 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-05 02:11:57.531 [info] > git status -z -uall [7ms]
2025-06-05 02:11:57.532 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:12:02.549 [info] > git config --get commit.template [8ms]
2025-06-05 02:12:02.550 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:12:02.563 [info] > git status -z -uall [8ms]
2025-06-05 02:12:02.565 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:12:07.607 [info] > git config --get commit.template [17ms]
2025-06-05 02:12:07.612 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-05 02:12:07.640 [info] > git status -z -uall [16ms]
2025-06-05 02:12:07.645 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-05 02:12:12.658 [info] > git config --get commit.template [3ms]
2025-06-05 02:12:12.667 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:12:12.686 [info] > git status -z -uall [9ms]
2025-06-05 02:12:12.687 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:12:43.340 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:12:43.340 [info] > git config --get commit.template [10ms]
2025-06-05 02:12:43.360 [info] > git status -z -uall [11ms]
2025-06-05 02:12:43.361 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 02:12:48.377 [info] > git config --get commit.template [7ms]
2025-06-05 02:12:48.378 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:12:48.391 [info] > git status -z -uall [8ms]
2025-06-05 02:12:48.392 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:12:53.411 [info] > git config --get commit.template [8ms]
2025-06-05 02:12:53.412 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:12:53.434 [info] > git status -z -uall [13ms]
2025-06-05 02:12:53.434 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 02:12:58.447 [info] > git config --get commit.template [4ms]
2025-06-05 02:12:58.448 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:12:58.457 [info] > git status -z -uall [5ms]
2025-06-05 02:12:58.458 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:13:03.470 [info] > git config --get commit.template [0ms]
2025-06-05 02:13:03.482 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:13:03.499 [info] > git status -z -uall [11ms]
2025-06-05 02:13:03.502 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 02:13:08.521 [info] > git config --get commit.template [8ms]
2025-06-05 02:13:08.522 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:13:08.536 [info] > git status -z -uall [7ms]
2025-06-05 02:13:08.537 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:13:13.554 [info] > git config --get commit.template [6ms]
2025-06-05 02:13:13.555 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:13:13.577 [info] > git status -z -uall [12ms]
2025-06-05 02:13:13.577 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-05 02:13:18.593 [info] > git config --get commit.template [6ms]
2025-06-05 02:13:18.594 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:13:18.609 [info] > git status -z -uall [6ms]
2025-06-05 02:13:18.609 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 02:13:23.625 [info] > git config --get commit.template [2ms]
2025-06-05 02:13:23.635 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:13:23.662 [info] > git status -z -uall [18ms]
2025-06-05 02:13:23.663 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:13:28.680 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:13:28.681 [info] > git config --get commit.template [7ms]
2025-06-05 02:13:28.695 [info] > git status -z -uall [7ms]
2025-06-05 02:13:28.696 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:13:33.716 [info] > git config --get commit.template [9ms]
2025-06-05 02:13:33.717 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:13:33.734 [info] > git status -z -uall [9ms]
2025-06-05 02:13:33.735 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:13:38.755 [info] > git config --get commit.template [9ms]
2025-06-05 02:13:38.757 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:13:38.769 [info] > git status -z -uall [6ms]
2025-06-05 02:13:38.770 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:13:43.785 [info] > git config --get commit.template [6ms]
2025-06-05 02:13:43.787 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:13:43.804 [info] > git status -z -uall [8ms]
2025-06-05 02:13:43.805 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:13:48.839 [info] > git config --get commit.template [10ms]
2025-06-05 02:13:48.860 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-05 02:13:48.874 [info] > git status -z -uall [7ms]
2025-06-05 02:13:48.875 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:13:53.891 [info] > git config --get commit.template [7ms]
2025-06-05 02:13:53.892 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:13:53.907 [info] > git status -z -uall [9ms]
2025-06-05 02:13:53.908 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:13:58.932 [info] > git config --get commit.template [9ms]
2025-06-05 02:13:58.933 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:13:58.951 [info] > git status -z -uall [9ms]
2025-06-05 02:13:58.952 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-05 02:14:03.992 [info] > git config --get commit.template [6ms]
2025-06-05 02:14:03.993 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:14:04.008 [info] > git status -z -uall [8ms]
2025-06-05 02:14:04.009 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-05 02:14:09.025 [info] > git config --get commit.template [7ms]
2025-06-05 02:14:09.026 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:14:09.041 [info] > git status -z -uall [5ms]
2025-06-05 02:14:09.042 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:14:14.053 [info] > git config --get commit.template [3ms]
2025-06-05 02:14:14.063 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:14:14.081 [info] > git status -z -uall [10ms]
2025-06-05 02:14:14.083 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 02:14:19.099 [info] > git config --get commit.template [6ms]
2025-06-05 02:14:19.100 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:14:19.117 [info] > git status -z -uall [8ms]
2025-06-05 02:14:19.118 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:14:24.132 [info] > git config --get commit.template [6ms]
2025-06-05 02:14:24.133 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:14:24.148 [info] > git status -z -uall [8ms]
2025-06-05 02:14:24.211 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [65ms]
2025-06-05 02:14:29.227 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-05 02:14:29.228 [info] > git config --get commit.template [8ms]
2025-06-05 02:14:29.242 [info] > git status -z -uall [7ms]
2025-06-05 02:14:29.245 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-05 02:14:34.260 [info] > git config --get commit.template [6ms]
2025-06-05 02:14:34.261 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:14:34.271 [info] > git status -z -uall [5ms]
2025-06-05 02:14:34.272 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:14:39.294 [info] > git config --get commit.template [11ms]
2025-06-05 02:14:39.297 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-05 02:14:39.313 [info] > git status -z -uall [7ms]
2025-06-05 02:14:39.314 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-05 02:14:44.340 [info] > git config --get commit.template [10ms]
2025-06-05 02:14:44.342 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-05 02:14:44.360 [info] > git status -z -uall [7ms]
2025-06-05 02:14:44.362 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
