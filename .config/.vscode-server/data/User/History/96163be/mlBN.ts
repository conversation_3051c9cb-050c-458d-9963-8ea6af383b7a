import { WebSocket } from 'ws';
import { EventEmitter } from 'events';

interface RealtimeProxyConfig {
  apiKey: string;
  model: string;
  voice?: string;
  instructions?: string;
  temperature?: number;
}

export class OpenAIRealtimeProxy extends EventEmitter {
  private openaiWs: WebSocket | null = null;
  private clientWs: WebSocket | null = null;
  private config: RealtimeProxyConfig;
  private isConnected = false;
  private sessionId: string | null = null;
  private isDisconnecting = false;
  public onTranscriptUpdate?: (speaker: 'user' | 'ai', text: string) => void;
  private currentAITranscript = '';
  private currentUserTranscript = '';
  private initialGreetingSent = false;
  private hasActiveResponse = false;

  constructor(config: RealtimeProxyConfig) {
    super();
    this.config = config;
  }

  async connect(clientWebSocket: WebSocket): Promise<void> {
    this.clientWs = clientWebSocket;

    try {
      // Connect to OpenAI's Realtime API
      const openaiUrl = `wss://api.openai.com/v1/realtime?model=${this.config.model}`;
      
      this.openaiWs = new WebSocket(openaiUrl, {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'OpenAI-Beta': 'realtime=v1'
        }
      });

      this.setupOpenAIEventHandlers();
      // Don't set up client event handlers here - let the main handler route messages

      // Wait for OpenAI connection
      await new Promise<void>((resolve, reject) => {
        this.openaiWs!.once('open', () => {
          console.log('Connected to OpenAI Realtime API');
          this.isConnected = true;
          this.initializeSession();
          resolve();
        });

        this.openaiWs!.once('error', (error) => {
          console.error('Failed to connect to OpenAI Realtime API:', error);
          reject(error);
        });
      });

    } catch (error) {
      console.error('Error connecting to OpenAI Realtime API:', error);
      throw error;
    }
  }

  private setupOpenAIEventHandlers(): void {
    if (!this.openaiWs) return;

    this.openaiWs.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        console.log('OpenAI → Client:', message.type);

        // Handle specific message types
        switch (message.type) {
          case 'session.created':
            this.sessionId = message.session.id;
            console.log('OpenAI session created:', this.sessionId);
            this.sendToClient(message); // Forward to client
            break;
          
                  case 'session.updated':
          console.log('OpenAI session updated');
          
          // Only trigger initial greeting once per session
          if (!this.initialGreetingSent) {
            this.initialGreetingSent = true;
            
            // Notify client that we're ready
            this.sendToClient({
              type: 'ready',
              message: 'OpenAI Realtime session initialized'
            });
            
            // Trigger initial AI greeting only once
            console.log('Triggering initial AI greeting...');
            this.sendToOpenAI({
              type: 'conversation.item.create',
              item: {
                type: 'message',
                role: 'system',
                content: [
                  {
                    type: 'input_text',
                    text: 'The user has just started a voice therapy session. Please greet them warmly and invite them to share what\'s on their mind today. Keep it natural and conversational.'
                  }
                ]
              }
            });
            
            // Create response to trigger AI to speak
            this.sendToOpenAI({
              type: 'response.create'
            });
          } else {
            console.log('Session configuration updated (VAD switch) - no new greeting needed');
          }
          
          // Always forward session.updated to client
          this.sendToClient(message);
          break;

          case 'conversation.item.input_audio_transcription.completed':
            // User speech transcription - capture for summary
            if (message.transcript && this.onTranscriptUpdate) {
              console.log('Capturing user transcript:', message.transcript);
              this.onTranscriptUpdate('user', message.transcript);
            }
            this.sendToClient(message);
            break;
            
          case 'response.audio_transcript.delta':
            // Accumulate AI transcript
            if (message.delta) {
              this.currentAITranscript += message.delta;
            }
            this.sendToClient(message);
            break;
            
          case 'response.audio_transcript.done':
            // AI speech transcription complete - capture for summary  
            if (this.currentAITranscript && this.onTranscriptUpdate) {
              console.log('Capturing AI transcript:', this.currentAITranscript);
              this.onTranscriptUpdate('ai', this.currentAITranscript);
              this.currentAITranscript = ''; // Reset for next response
            }
            this.sendToClient(message);
            break;

          case 'input_audio_buffer.speech_started':
          case 'input_audio_buffer.speech_stopped':
          case 'conversation.item.created':
          case 'response.created':
            // Track that we have an active response
            this.hasActiveResponse = true;
            this.sendToClient(message);
            break;

          case 'response.output_item.added':
          case 'response.content_part.added':
          case 'response.audio.delta':
          case 'response.audio.done':
          case 'response.text.delta':
          case 'response.text.done':
          case 'response.output_item.done':
            // Forward these events directly to the client
            this.sendToClient(message);
            break;

          case 'response.done':
            // Response is complete, clear the active flag
            this.hasActiveResponse = false;
            this.sendToClient(message);
            break;

          case 'rate_limits.updated':
            // Rate limit information - just log and forward
            console.log('OpenAI rate limits updated');
            this.sendToClient(message);
            break;
            
          case 'response.content_part.done':
            // Content part completion - forward to client
            this.sendToClient(message);
            break;

          case 'error':
            console.error('OpenAI API error:', message);
            this.sendToClient({
              type: 'error',
              message: message.error?.message || 'OpenAI API error'
            });
            break;

          default:
            console.log('Unhandled OpenAI message type:', message.type);
            // Forward unknown messages to client for debugging
            this.sendToClient(message);
        }
      } catch (error) {
        console.error('Error parsing OpenAI message:', error);
      }
    });

    this.openaiWs.on('close', (code, reason) => {
      console.log('OpenAI connection closed:', code, reason.toString());
      this.isConnected = false;
      
      // Only send error message if this wasn't an intentional disconnect
      if (!this.isDisconnecting) {
        this.sendToClient({
          type: 'error',
          message: 'OpenAI connection closed'
        });
      }
    });

    this.openaiWs.on('error', (error) => {
      console.error('OpenAI WebSocket error:', error);
      this.sendToClient({
        type: 'error',
        message: 'OpenAI connection error'
      });
    });
  }

  private initializeSession(): void {
    if (!this.openaiWs || !this.isConnected) return;

    // Configure session with OpenAI's server-side VAD for better speech detection
    const sessionConfig = {
      type: 'session.update',
      session: {
        modalities: ['text', 'audio'],
        instructions: this.config.instructions || 'You are Vale, an empathetic AI therapeutic assistant. Respond thoughtfully and supportively to help users process their emotions and thoughts.',
        voice: this.config.voice || 'shimmer',
        input_audio_format: 'pcm16',
        output_audio_format: 'pcm16',
        input_audio_transcription: {
          model: 'whisper-1'
        },
        turn_detection: {
          type: 'server_vad',
          threshold: 0.5,
          prefix_padding_ms: 300,
          silence_duration_ms: 500
        },
        temperature: this.config.temperature || 0.7,
        max_response_output_tokens: 1000
      }
    };

    console.log('🎯 Configuring session with SERVER-SIDE VAD (OpenAI handles speech detection)...');
    console.log('📊 Turn Detection: ENABLED (threshold: 0.5, silence: 500ms)');
    this.sendToOpenAI(sessionConfig);

    console.log('✅ Server-side VAD configured - OpenAI will handle speech detection');
  }

  private sendToOpenAI(message: any): void {
    if (this.openaiWs && this.isConnected) {
      this.openaiWs.send(JSON.stringify(message));
    } else {
      console.warn('Cannot send to OpenAI: not connected');
    }
  }

  private sendToClient(message: any): void {
    if (this.clientWs && this.clientWs.readyState === WebSocket.OPEN) {
      this.clientWs.send(JSON.stringify(message));
    }
  }

  private convertAudioFormat(audioData: number[]): string {
    // Convert Int16Array audio data to base64 PCM16
    const int16Array = new Int16Array(audioData);
    const buffer = Buffer.from(int16Array.buffer);
    return buffer.toString('base64');
  }

  private convertAudioArrayToBase64(audioData: number[], format?: string): string {
    if (format === 'int16') {
      // Already in correct format
      const int16Array = new Int16Array(audioData);
      const buffer = Buffer.from(int16Array.buffer);
      return buffer.toString('base64');
    } else {
      // Assume float32, convert to int16
      const int16Array = new Int16Array(audioData.length);
      for (let i = 0; i < audioData.length; i++) {
        const sample = Math.max(-1, Math.min(1, audioData[i]));
        int16Array[i] = Math.floor(sample < 0 ? sample * 32768 : sample * 32767);
      }
      const buffer = Buffer.from(int16Array.buffer);
      return buffer.toString('base64');
    }
  }

  public disconnect(): void {
    this.isDisconnecting = true;

    if (this.openaiWs) {
      this.openaiWs.close();
      this.openaiWs = null;
    }
    this.isConnected = false;
    this.sessionId = null;
    this.hasActiveResponse = false;
  }

  public getSessionId(): string | null {
    return this.sessionId;
  }

  public isSessionActive(): boolean {
    return this.isConnected && this.sessionId !== null;
  }
  
  // Handle messages routed from the main websocket handler
  public handleClientMessage(message: any): void {
    try {
      console.log('Realtime proxy handling message:', message.type);

      // Handle client messages
      switch (message.type) {
        case 'start':
          // Client wants to start - we already initialized, just confirm
          this.sendToClient({
            type: 'ready',
            message: 'Session already active'
          });
          break;

        case 'generate_test_audio':
          // Generate test audio for debugging
          this.generateTestAudio(message.text || "This is a test of the voice system. Please respond when ready.");
          break;

        case 'test_round_trip':
          // Test complete round-trip audio flow
          this.testRoundTripAudio();
          break;

        case 'input_audio_buffer.append':
          // Forward audio data to OpenAI with enhanced debugging
          const audioData = message.audio_data || message.audio;
          if (audioData) {
            console.log(`📤 Forwarding audio to OpenAI: ${typeof audioData} (${typeof audioData === 'string' ? audioData.length + ' chars' : 'non-string'})`);
            
            // Enhanced audio validation and debugging
            if (typeof audioData === 'string' && audioData.length > 0) {
              try {
                const decoded = Buffer.from(audioData, 'base64');
                const samples = decoded.length / 2; // PCM16 = 2 bytes per sample
                console.log(`📊 Audio info: ${decoded.length} bytes, ${samples} samples`);
                
                // Analyze audio content for speech-like characteristics
                const int16Array = new Int16Array(decoded.buffer);
                let energy = 0;
                let peakCount = 0;
                let avgAmplitude = 0;
                
                for (let i = 0; i < int16Array.length; i++) {
                  const sample = Math.abs(int16Array[i]);
                  energy += sample;
                  avgAmplitude += sample;
                  
                  // Count peaks (potential speech indicators)
                  if (sample > 1000) {
                    peakCount++;
                  }
                }
                
                const avgEnergy = energy / int16Array.length;
                avgAmplitude = avgAmplitude / int16Array.length;
                const peakRatio = peakCount / int16Array.length;
                
                console.log(`🔊 Audio analysis: avg=${avgEnergy.toFixed(2)}, amp=${avgAmplitude.toFixed(2)}, peaks=${(peakRatio*100).toFixed(2)}%`);
                
                // Determine if this looks like speech
                const looksLikeSpeech = avgEnergy > 100 && peakRatio > 0.01;
                console.log(`🎯 Speech likelihood: ${looksLikeSpeech ? 'HIGH' : 'LOW'} (energy=${avgEnergy > 100}, peaks=${peakRatio > 0.01})`);
                
              } catch (e) {
                console.warn('Failed to analyze audio data:', e instanceof Error ? e.message : 'Unknown error');
              }
            }
            
            this.sendToOpenAI({
              type: 'input_audio_buffer.append',
              audio: message.audio_data ? this.convertAudioFormat(message.audio_data) : message.audio
            });
          } else {
            console.warn('No audio data in input_audio_buffer.append message');
          }
          break;

        case 'input_audio_buffer.commit':
          // Forward commit to OpenAI
          this.sendToOpenAI(message);

          // Since we're using manual VAD, automatically create a response after commit
          // But only if there's no active response already
          setTimeout(() => {
            if (this.openaiWs && this.isConnected && !this.hasActiveResponse) {
              console.log('🤖 Auto-creating response after manual audio commit');
              this.sendToOpenAI({
                type: 'response.create'
              });
            } else if (this.hasActiveResponse) {
              console.log('⏸️ Skipping auto-response creation - response already active');
            }
          }, 100); // Small delay to ensure commit is processed
          break;

        case 'response.create':
          // Only create response if none is active
          if (!this.hasActiveResponse) {
            this.sendToOpenAI(message);
          } else {
            console.log('⏸️ Skipping response.create - response already active');
          }
          break;

        case 'response.cancel':
          // Clear active response flag when cancelled
          this.hasActiveResponse = false;
          this.sendToOpenAI(message);
          break;

        case 'conversation.item.create':
        case 'conversation.item.delete':
        case 'conversation.item.truncate':
          // Forward these commands directly to OpenAI
          this.sendToOpenAI(message);
          break;

        case 'audio_chunk':
          // Convert legacy audio_chunk to OpenAI format
          if (message.audio_data && Array.isArray(message.audio_data)) {
            const base64Audio = this.convertAudioArrayToBase64(message.audio_data, message.format);
            this.sendToOpenAI({
              type: 'input_audio_buffer.append',
              audio: base64Audio
            });
          }
          break;

        case 'text':
          // Convert text message to OpenAI conversation item
          this.sendToOpenAI({
            type: 'conversation.item.create',
            item: {
              type: 'message',
              role: 'user',
              content: [
                {
                  type: 'input_text',
                  text: message.content
                }
              ]
            }
          });
          // Trigger response
          this.sendToOpenAI({
            type: 'response.create'
          });
          break;

        default:
          console.log('Unhandled client message type in proxy:', message.type);
      }
    } catch (error) {
      console.error('Error handling client message in proxy:', error);
    }
  }

  // Add a method to generate test audio for debugging
  public generateTestAudio(testPhrase: string = "Hello, I am testing the voice system. Can you hear me?"): void {
    if (!this.openaiWs || !this.isConnected) {
      console.warn('Cannot generate test audio: not connected to OpenAI');
      return;
    }

    console.log('🎤 Generating test audio for debugging...');
    
    // Create a conversation item with the test phrase
    this.sendToOpenAI({
      type: 'conversation.item.create',
      item: {
        type: 'message',
        role: 'user',
        content: [
          {
            type: 'input_text',
            text: testPhrase
          }
        ]
      }
    });
    
    // Trigger response
    this.sendToOpenAI({
      type: 'response.create'
    });
  }

  // Add a method to test round-trip audio
  public testRoundTripAudio(): void {
    console.log('🔄 Starting round-trip audio test...');
    
    // Generate a test phrase that we can then try to "speak" back
    this.generateTestAudio("Please say hello back to me when you're ready to test the voice system.");
  }
} 