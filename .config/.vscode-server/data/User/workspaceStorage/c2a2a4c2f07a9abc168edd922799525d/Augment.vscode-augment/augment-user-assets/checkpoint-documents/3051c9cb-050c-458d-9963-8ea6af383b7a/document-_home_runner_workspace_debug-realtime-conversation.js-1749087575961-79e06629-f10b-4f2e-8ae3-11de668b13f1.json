{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "debug-realtime-conversation.js"}, "modifiedCode": "#!/usr/bin/env node\n\nimport { WebSocket } from 'ws';\n\nconst WEBSOCKET_URL = 'ws://localhost:5000/ws';\n\nconsole.log('🔍 Debugging Real-time Conversation Flow...');\nconsole.log('📡 Connecting to:', WEBSOCKET_URL);\n\nconst ws = new WebSocket(WEBSOCKET_URL);\n\nlet messageCount = 0;\nconst receivedMessages = [];\nconst sentMessages = [];\n\nws.on('open', () => {\n  console.log('✅ WebSocket connected');\n  \n  // Send start message to initialize Realtime API session\n  const startMessage = {\n    type: \"start\",\n    userId: \"debug-user\",\n    clientId: \"debug-client\",\n    useRealtimeAPI: true,\n    mode: \"realtime\",\n    behavior: {\n      model: \"gpt-4o-realtime-preview\",\n      temperature: 0.7,\n      voice: {\n        voice: \"shimmer\",\n        speed: 1.0\n      }\n    },\n    instructions: \"You are <PERSON>, an empathetic AI therapeutic assistant. Respond thoughtfully and supportively to help users process their emotions and thoughts. Keep responses concise and natural for voice conversation.\"\n  };\n  \n  console.log('📤 Sending start message...');\n  ws.send(JSON.stringify(startMessage));\n  sentMessages.push('start');\n});\n\nws.on('message', (data) => {\n  messageCount++;\n  try {\n    const message = JSON.parse(data.toString());\n    console.log(`📥 Message ${messageCount}: ${message.type}`);\n    receivedMessages.push(message.type);\n    \n    // Handle specific message types\n    switch (message.type) {\n      case 'ready':\n        console.log('✅ Session ready! Testing audio input simulation...');\n        \n        // Simulate sending audio data after a short delay\n        setTimeout(() => {\n          console.log('🎤 Simulating audio input...');\n          \n          // Generate some fake PCM16 audio data (silence with a bit of noise)\n          const sampleRate = 24000;\n          const durationMs = 1000; // 1 second\n          const samples = Math.floor(sampleRate * durationMs / 1000);\n          const audioBuffer = new Int16Array(samples);\n          \n          // Add some random noise to simulate speech\n          for (let i = 0; i < samples; i++) {\n            audioBuffer[i] = Math.floor((Math.random() - 0.5) * 1000); // Low amplitude noise\n          }\n          \n          // Convert to base64\n          const buffer = Buffer.from(audioBuffer.buffer);\n          const base64Audio = buffer.toString('base64');\n          \n          console.log(`📤 Sending audio chunk (${base64Audio.length} chars)...`);\n          \n          // Send audio data\n          ws.send(JSON.stringify({\n            type: 'input_audio_buffer.append',\n            audio: base64Audio\n          }));\n          sentMessages.push('input_audio_buffer.append');\n          \n          // Commit the audio after a short delay\n          setTimeout(() => {\n            console.log('📤 Committing audio buffer...');\n            ws.send(JSON.stringify({\n              type: 'input_audio_buffer.commit'\n            }));\n            sentMessages.push('input_audio_buffer.commit');\n          }, 100);\n          \n        }, 1000);\n        break;\n        \n      case 'conversation.item.input_audio_transcription.completed':\n        console.log(`🎯 User speech transcribed: \"${message.transcript}\"`);\n        break;\n        \n      case 'response.audio.delta':\n        console.log('🔊 AI audio response chunk received');\n        break;\n        \n      case 'response.audio_transcript.done':\n        console.log(`🤖 AI response: \"${message.transcript}\"`);\n        break;\n        \n      case 'response.done':\n        console.log('✅ Response completed');\n        \n        // End the test after receiving a complete response\n        setTimeout(() => {\n          console.log('📤 Ending conversation...');\n          ws.send(JSON.stringify({\n            type: 'conversationEnded'\n          }));\n          sentMessages.push('conversationEnded');\n        }, 1000);\n        break;\n        \n      case 'end_ack':\n        console.log('✅ Conversation ended');\n        \n        // Close connection and show summary\n        setTimeout(() => {\n          console.log('\\n=== DEBUG SUMMARY ===');\n          console.log('Sent messages:', sentMessages);\n          console.log('Received messages:', receivedMessages);\n          console.log('✅ Debug test completed');\n          ws.close();\n        }, 500);\n        break;\n        \n      case 'error':\n        console.error('❌ Error received:', message.message);\n        break;\n        \n      default:\n        // Just log other message types\n        break;\n    }\n  } catch (error) {\n    console.error('❌ Error parsing message:', error);\n  }\n});\n\nws.on('close', (code, reason) => {\n  console.log(`🔌 WebSocket closed: ${code} ${reason.toString()}`);\n});\n\nws.on('error', (error) => {\n  console.error('❌ WebSocket error:', error);\n});\n\n// Timeout to prevent hanging\nsetTimeout(() => {\n  console.log('⏰ Test timeout - closing connection');\n  ws.close();\n  process.exit(0);\n}, 30000); // 30 second timeout\n"}