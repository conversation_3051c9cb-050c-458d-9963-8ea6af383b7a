{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "test-complete-conversation.js"}, "modifiedCode": "#!/usr/bin/env node\n\nimport { WebSocket } from 'ws';\n\nconst WEBSOCKET_URL = 'ws://localhost:5000/ws';\n\nconsole.log('🗣️ Testing Complete Real-time Conversation with Server VAD...');\nconsole.log('📡 Connecting to:', WEBSOCKET_URL);\n\nconst ws = new WebSocket(WEBSOCKET_URL);\n\nlet messageCount = 0;\nlet conversationStep = 0;\nlet speechStarted = false;\nlet speechEnded = false;\nlet responseReceived = false;\n\nws.on('open', () => {\n  console.log('✅ WebSocket connected');\n  \n  const startMessage = {\n    type: \"start\",\n    userId: \"conversation-test\",\n    clientId: \"conversation-test\",\n    useRealtimeAPI: true,\n    mode: \"realtime\",\n    behavior: {\n      model: \"gpt-4o-realtime-preview-2025-06-03\",\n      temperature: 0.7,\n      voice: {\n        voice: \"shimmer\",\n        speed: 1.0\n      }\n    },\n    instructions: \"You are <PERSON>, an empathetic AI therapeutic assistant. Keep responses very brief (1-2 sentences) for this test conversation.\"\n  };\n  \n  console.log('📤 Starting conversation with server-side VAD...');\n  ws.send(JSON.stringify(startMessage));\n});\n\nws.on('message', (data) => {\n  messageCount++;\n  try {\n    const message = JSON.parse(data.toString());\n    \n    // Only log important events\n    if (['ready', 'input_audio_buffer.speech_started', 'input_audio_buffer.speech_stopped', \n         'conversation.item.input_audio_transcription.completed', 'response.audio_transcript.done', \n         'response.done', 'error'].includes(message.type)) {\n      console.log(`📥 ${message.type}`);\n    }\n    \n    switch (message.type) {\n      case 'ready':\n        console.log('✅ Session ready! Waiting for AI greeting...');\n        break;\n        \n      case 'response.done':\n        if (conversationStep === 0) {\n          console.log('✅ AI greeting completed. Starting user speech simulation...');\n          conversationStep = 1;\n          \n          // Start speaking after AI finishes greeting\n          setTimeout(() => {\n            startSpeechSimulation();\n          }, 1000);\n        } else {\n          console.log('✅ AI response to user completed!');\n          responseReceived = true;\n          \n          // End conversation after successful response\n          setTimeout(() => {\n            console.log('📤 Ending conversation...');\n            ws.send(JSON.stringify({\n              type: 'conversationEnded'\n            }));\n          }, 1000);\n        }\n        break;\n        \n      case 'input_audio_buffer.speech_started':\n        console.log('🎤 SERVER VAD: Speech detection started!');\n        speechStarted = true;\n        break;\n        \n      case 'input_audio_buffer.speech_stopped':\n        console.log('🔇 SERVER VAD: Speech detection stopped!');\n        speechEnded = true;\n        break;\n        \n      case 'conversation.item.input_audio_transcription.completed':\n        console.log(`🎯 User speech transcribed: \"${message.transcript}\"`);\n        break;\n        \n      case 'response.audio_transcript.done':\n        console.log(`🤖 AI response: \"${message.transcript}\"`);\n        break;\n        \n      case 'end_ack':\n        console.log('✅ Conversation ended successfully');\n        \n        setTimeout(() => {\n          console.log('\\n=== COMPLETE CONVERSATION TEST SUMMARY ===');\n          console.log(`✅ Speech started detected: ${speechStarted}`);\n          console.log(`✅ Speech stopped detected: ${speechEnded}`);\n          console.log(`✅ AI response received: ${responseReceived}`);\n          console.log(`📊 Total messages: ${messageCount}`);\n          \n          if (speechStarted && speechEnded && responseReceived) {\n            console.log('🎉 COMPLETE CONVERSATION TEST PASSED!');\n          } else {\n            console.log('❌ Test incomplete - missing some events');\n          }\n          \n          ws.close();\n        }, 500);\n        break;\n        \n      case 'error':\n        console.error('❌ Error:', message.message);\n        break;\n    }\n  } catch (error) {\n    console.error('❌ Error parsing message:', error);\n  }\n});\n\n// Function to simulate realistic speech with proper timing\nfunction startSpeechSimulation() {\n  console.log('🎤 Starting speech simulation...');\n  \n  let chunkCount = 0;\n  const speechDurationMs = 3000; // 3 seconds of speech\n  const chunkIntervalMs = 50; // 50ms chunks\n  const totalChunks = speechDurationMs / chunkIntervalMs;\n  \n  const speechInterval = setInterval(() => {\n    chunkCount++;\n    \n    // Generate speech-like audio with realistic patterns\n    const sampleRate = 24000;\n    const samples = Math.floor(sampleRate * chunkIntervalMs / 1000);\n    const audioBuffer = new Int16Array(samples);\n    \n    // Create realistic speech intensity curve\n    const progress = chunkCount / totalChunks;\n    const speechIntensity = 0.6 * Math.sin(progress * Math.PI); // Bell curve intensity\n    \n    for (let i = 0; i < samples; i++) {\n      const time = (chunkCount * chunkIntervalMs / 1000) + (i / sampleRate);\n      \n      // Multiple frequency components for speech-like sound\n      const f1 = 200 + Math.sin(time * 8) * 50;  // Fundamental frequency\n      const f2 = 800 + Math.sin(time * 12) * 100; // First formant\n      const f3 = 2400 + Math.sin(time * 6) * 200; // Second formant\n      \n      const amplitude = speechIntensity * (0.7 + 0.3 * Math.sin(time * 15));\n      \n      // Combine frequencies with different amplitudes\n      const signal = (\n        Math.sin(2 * Math.PI * f1 * time) * 0.5 +\n        Math.sin(2 * Math.PI * f2 * time) * 0.3 +\n        Math.sin(2 * Math.PI * f3 * time) * 0.2\n      ) * amplitude;\n      \n      // Add some noise for realism\n      const noise = (Math.random() - 0.5) * 0.1 * amplitude;\n      \n      audioBuffer[i] = Math.floor((signal + noise) * 12000);\n    }\n    \n    // Convert to base64 and send\n    const buffer = Buffer.from(audioBuffer.buffer);\n    const base64Audio = buffer.toString('base64');\n    \n    ws.send(JSON.stringify({\n      type: 'input_audio_buffer.append',\n      audio: base64Audio\n    }));\n    \n    // Log progress every 20 chunks\n    if (chunkCount % 20 === 0) {\n      console.log(`📤 Speech progress: ${Math.round(progress * 100)}%`);\n    }\n    \n    // Stop after total chunks\n    if (chunkCount >= totalChunks) {\n      clearInterval(speechInterval);\n      console.log('🔇 Speech simulation completed - waiting for server VAD to detect end...');\n    }\n  }, chunkIntervalMs);\n}\n\nws.on('close', (code, reason) => {\n  console.log(`🔌 Connection closed: ${code} ${reason.toString()}`);\n});\n\nws.on('error', (error) => {\n  console.error('❌ WebSocket error:', error);\n});\n\n// Timeout to prevent hanging\nsetTimeout(() => {\n  console.log('⏰ Test timeout - ending test');\n  ws.close();\n  process.exit(0);\n}, 45000); // 45 second timeout\n"}