{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/openai-realtime-proxy.ts"}, "originalCode": "import { WebSocket } from 'ws';\nimport { EventEmitter } from 'events';\n\ninterface RealtimeProxyConfig {\n  apiKey: string;\n  model: string;\n  voice?: string;\n  instructions?: string;\n  temperature?: number;\n}\n\nexport class OpenAIRealtimeProxy extends EventEmitter {\n  private openaiWs: WebSocket | null = null;\n  private clientWs: WebSocket | null = null;\n  private config: RealtimeProxyConfig;\n  private isConnected = false;\n  private sessionId: string | null = null;\n  private isDisconnecting = false;\n  public onTranscriptUpdate?: (speaker: 'user' | 'ai', text: string) => void;\n  private currentAITranscript = '';\n  private currentUserTranscript = '';\n  private initialGreetingSent = false;\n  private hasActiveResponse = false;\n\n  constructor(config: RealtimeProxyConfig) {\n    super();\n    this.config = config;\n  }\n\n  async connect(clientWebSocket: WebSocket): Promise<void> {\n    this.clientWs = clientWebSocket;\n\n    try {\n      // Connect to OpenAI's Realtime API\n      const openaiUrl = `wss://api.openai.com/v1/realtime?model=${this.config.model}`;\n      \n      this.openaiWs = new WebSocket(openaiUrl, {\n        headers: {\n          'Authorization': `Bearer ${this.config.apiKey}`,\n          'OpenAI-Beta': 'realtime=v1'\n        }\n      });\n\n      this.setupOpenAIEventHandlers();\n      // Don't set up client event handlers here - let the main handler route messages\n\n      // Wait for OpenAI connection\n      await new Promise<void>((resolve, reject) => {\n        this.openaiWs!.once('open', () => {\n          console.log('Connected to OpenAI Realtime API');\n          this.isConnected = true;\n          this.initializeSession();\n          resolve();\n        });\n\n        this.openaiWs!.once('error', (error) => {\n          console.error('Failed to connect to OpenAI Realtime API:', error);\n          reject(error);\n        });\n      });\n\n    } catch (error) {\n      console.error('Error connecting to OpenAI Realtime API:', error);\n      throw error;\n    }\n  }\n\n  private setupOpenAIEventHandlers(): void {\n    if (!this.openaiWs) return;\n\n    this.openaiWs.on('message', (data) => {\n      try {\n        const message = JSON.parse(data.toString());\n        console.log('OpenAI → Client:', message.type);\n\n        // Handle specific message types\n        switch (message.type) {\n          case 'session.created':\n            this.sessionId = message.session.id;\n            console.log('OpenAI session created:', this.sessionId);\n            this.sendToClient(message); // Forward to client\n            break;\n          \n                  case 'session.updated':\n          console.log('OpenAI session updated');\n          \n          // Only trigger initial greeting once per session\n          if (!this.initialGreetingSent) {\n            this.initialGreetingSent = true;\n            \n            // Notify client that we're ready\n            this.sendToClient({\n              type: 'ready',\n              message: 'OpenAI Realtime session initialized'\n            });\n            \n            // Trigger initial AI greeting only once\n            console.log('Triggering initial AI greeting...');\n            this.sendToOpenAI({\n              type: 'conversation.item.create',\n              item: {\n                type: 'message',\n                role: 'system',\n                content: [\n                  {\n                    type: 'input_text',\n                    text: 'The user has just started a voice therapy session. Please greet them warmly and invite them to share what\\'s on their mind today. Keep it natural and conversational.'\n                  }\n                ]\n              }\n            });\n            \n            // Create response to trigger AI to speak\n            this.sendToOpenAI({\n              type: 'response.create'\n            });\n          } else {\n            console.log('Session configuration updated (VAD switch) - no new greeting needed');\n          }\n          \n          // Always forward session.updated to client\n          this.sendToClient(message);\n          break;\n\n          case 'conversation.item.input_audio_transcription.completed':\n            // User speech transcription - capture for summary\n            if (message.transcript && this.onTranscriptUpdate) {\n              console.log('Capturing user transcript:', message.transcript);\n              this.onTranscriptUpdate('user', message.transcript);\n            }\n            this.sendToClient(message);\n            break;\n            \n          case 'response.audio_transcript.delta':\n            // Accumulate AI transcript\n            if (message.delta) {\n              this.currentAITranscript += message.delta;\n            }\n            this.sendToClient(message);\n            break;\n            \n          case 'response.audio_transcript.done':\n            // AI speech transcription complete - capture for summary  \n            if (this.currentAITranscript && this.onTranscriptUpdate) {\n              console.log('Capturing AI transcript:', this.currentAITranscript);\n              this.onTranscriptUpdate('ai', this.currentAITranscript);\n              this.currentAITranscript = ''; // Reset for next response\n            }\n            this.sendToClient(message);\n            break;\n\n          case 'input_audio_buffer.speech_started':\n          case 'input_audio_buffer.speech_stopped':\n          case 'conversation.item.created':\n          case 'response.created':\n          case 'response.output_item.added':\n          case 'response.content_part.added':\n          case 'response.audio.delta':\n          case 'response.audio.done':\n          case 'response.text.delta':\n          case 'response.text.done':\n          case 'response.output_item.done':\n          case 'response.done':\n            // Forward these events directly to the client\n            this.sendToClient(message);\n            break;\n\n          case 'rate_limits.updated':\n            // Rate limit information - just log and forward\n            console.log('OpenAI rate limits updated');\n            this.sendToClient(message);\n            break;\n            \n          case 'response.content_part.done':\n            // Content part completion - forward to client\n            this.sendToClient(message);\n            break;\n\n          case 'error':\n            console.error('OpenAI API error:', message);\n            this.sendToClient({\n              type: 'error',\n              message: message.error?.message || 'OpenAI API error'\n            });\n            break;\n\n          default:\n            console.log('Unhandled OpenAI message type:', message.type);\n            // Forward unknown messages to client for debugging\n            this.sendToClient(message);\n        }\n      } catch (error) {\n        console.error('Error parsing OpenAI message:', error);\n      }\n    });\n\n    this.openaiWs.on('close', (code, reason) => {\n      console.log('OpenAI connection closed:', code, reason.toString());\n      this.isConnected = false;\n      \n      // Only send error message if this wasn't an intentional disconnect\n      if (!this.isDisconnecting) {\n        this.sendToClient({\n          type: 'error',\n          message: 'OpenAI connection closed'\n        });\n      }\n    });\n\n    this.openaiWs.on('error', (error) => {\n      console.error('OpenAI WebSocket error:', error);\n      this.sendToClient({\n        type: 'error',\n        message: 'OpenAI connection error'\n      });\n    });\n  }\n\n  private initializeSession(): void {\n    if (!this.openaiWs || !this.isConnected) return;\n\n    // Configure session with manual turn detection (client-side VAD)\n    const sessionConfig = {\n      type: 'session.update',\n      session: {\n        modalities: ['text', 'audio'],\n        instructions: this.config.instructions || 'You are Vale, an empathetic AI therapeutic assistant. Respond thoughtfully and supportively to help users process their emotions and thoughts.',\n        voice: this.config.voice || 'shimmer',\n        input_audio_format: 'pcm16',\n        output_audio_format: 'pcm16',\n        input_audio_transcription: {\n          model: 'whisper-1'\n        },\n        turn_detection: null, // Disable automatic turn detection - use client-side VAD\n        temperature: this.config.temperature || 0.7,\n        max_response_output_tokens: 1000\n      }\n    };\n\n    console.log('🎯 Configuring session with CLIENT-SIDE VAD (server VAD disabled)...');\n    console.log('📊 Turn Detection: DISABLED (manual commits from client)');\n    this.sendToOpenAI(sessionConfig);\n\n    console.log('✅ Client-side VAD configured - speech detection handled by browser');\n  }\n\n  private sendToOpenAI(message: any): void {\n    if (this.openaiWs && this.isConnected) {\n      this.openaiWs.send(JSON.stringify(message));\n    } else {\n      console.warn('Cannot send to OpenAI: not connected');\n    }\n  }\n\n  private sendToClient(message: any): void {\n    if (this.clientWs && this.clientWs.readyState === WebSocket.OPEN) {\n      this.clientWs.send(JSON.stringify(message));\n    }\n  }\n\n  private convertAudioFormat(audioData: number[]): string {\n    // Convert Int16Array audio data to base64 PCM16\n    const int16Array = new Int16Array(audioData);\n    const buffer = Buffer.from(int16Array.buffer);\n    return buffer.toString('base64');\n  }\n\n  private convertAudioArrayToBase64(audioData: number[], format?: string): string {\n    if (format === 'int16') {\n      // Already in correct format\n      const int16Array = new Int16Array(audioData);\n      const buffer = Buffer.from(int16Array.buffer);\n      return buffer.toString('base64');\n    } else {\n      // Assume float32, convert to int16\n      const int16Array = new Int16Array(audioData.length);\n      for (let i = 0; i < audioData.length; i++) {\n        const sample = Math.max(-1, Math.min(1, audioData[i]));\n        int16Array[i] = Math.floor(sample < 0 ? sample * 32768 : sample * 32767);\n      }\n      const buffer = Buffer.from(int16Array.buffer);\n      return buffer.toString('base64');\n    }\n  }\n\n  public disconnect(): void {\n    this.isDisconnecting = true;\n    \n    if (this.openaiWs) {\n      this.openaiWs.close();\n      this.openaiWs = null;\n    }\n    this.isConnected = false;\n    this.sessionId = null;\n  }\n\n  public getSessionId(): string | null {\n    return this.sessionId;\n  }\n\n  public isSessionActive(): boolean {\n    return this.isConnected && this.sessionId !== null;\n  }\n  \n  // Handle messages routed from the main websocket handler\n  public handleClientMessage(message: any): void {\n    try {\n      console.log('Realtime proxy handling message:', message.type);\n\n      // Handle client messages\n      switch (message.type) {\n        case 'start':\n          // Client wants to start - we already initialized, just confirm\n          this.sendToClient({\n            type: 'ready',\n            message: 'Session already active'\n          });\n          break;\n\n        case 'generate_test_audio':\n          // Generate test audio for debugging\n          this.generateTestAudio(message.text || \"This is a test of the voice system. Please respond when ready.\");\n          break;\n\n        case 'test_round_trip':\n          // Test complete round-trip audio flow\n          this.testRoundTripAudio();\n          break;\n\n        case 'input_audio_buffer.append':\n          // Forward audio data to OpenAI with enhanced debugging\n          const audioData = message.audio_data || message.audio;\n          if (audioData) {\n            console.log(`📤 Forwarding audio to OpenAI: ${typeof audioData} (${typeof audioData === 'string' ? audioData.length + ' chars' : 'non-string'})`);\n            \n            // Enhanced audio validation and debugging\n            if (typeof audioData === 'string' && audioData.length > 0) {\n              try {\n                const decoded = Buffer.from(audioData, 'base64');\n                const samples = decoded.length / 2; // PCM16 = 2 bytes per sample\n                console.log(`📊 Audio info: ${decoded.length} bytes, ${samples} samples`);\n                \n                // Analyze audio content for speech-like characteristics\n                const int16Array = new Int16Array(decoded.buffer);\n                let energy = 0;\n                let peakCount = 0;\n                let avgAmplitude = 0;\n                \n                for (let i = 0; i < int16Array.length; i++) {\n                  const sample = Math.abs(int16Array[i]);\n                  energy += sample;\n                  avgAmplitude += sample;\n                  \n                  // Count peaks (potential speech indicators)\n                  if (sample > 1000) {\n                    peakCount++;\n                  }\n                }\n                \n                const avgEnergy = energy / int16Array.length;\n                avgAmplitude = avgAmplitude / int16Array.length;\n                const peakRatio = peakCount / int16Array.length;\n                \n                console.log(`🔊 Audio analysis: avg=${avgEnergy.toFixed(2)}, amp=${avgAmplitude.toFixed(2)}, peaks=${(peakRatio*100).toFixed(2)}%`);\n                \n                // Determine if this looks like speech\n                const looksLikeSpeech = avgEnergy > 100 && peakRatio > 0.01;\n                console.log(`🎯 Speech likelihood: ${looksLikeSpeech ? 'HIGH' : 'LOW'} (energy=${avgEnergy > 100}, peaks=${peakRatio > 0.01})`);\n                \n              } catch (e) {\n                console.warn('Failed to analyze audio data:', e instanceof Error ? e.message : 'Unknown error');\n              }\n            }\n            \n            this.sendToOpenAI({\n              type: 'input_audio_buffer.append',\n              audio: message.audio_data ? this.convertAudioFormat(message.audio_data) : message.audio\n            });\n          } else {\n            console.warn('No audio data in input_audio_buffer.append message');\n          }\n          break;\n\n        case 'input_audio_buffer.commit':\n          // Forward commit to OpenAI\n          this.sendToOpenAI(message);\n          \n          // Since we're using manual VAD, automatically create a response after commit\n          setTimeout(() => {\n            if (this.openaiWs && this.isConnected) {\n              console.log('🤖 Auto-creating response after manual audio commit');\n              this.sendToOpenAI({\n                type: 'response.create'\n              });\n            }\n          }, 100); // Small delay to ensure commit is processed\n          break;\n\n        case 'response.create':\n        case 'response.cancel':\n        case 'conversation.item.create':\n        case 'conversation.item.delete':\n        case 'conversation.item.truncate':\n          // Forward these commands directly to OpenAI\n          this.sendToOpenAI(message);\n          break;\n\n        case 'audio_chunk':\n          // Convert legacy audio_chunk to OpenAI format\n          if (message.audio_data && Array.isArray(message.audio_data)) {\n            const base64Audio = this.convertAudioArrayToBase64(message.audio_data, message.format);\n            this.sendToOpenAI({\n              type: 'input_audio_buffer.append',\n              audio: base64Audio\n            });\n          }\n          break;\n\n        case 'text':\n          // Convert text message to OpenAI conversation item\n          this.sendToOpenAI({\n            type: 'conversation.item.create',\n            item: {\n              type: 'message',\n              role: 'user',\n              content: [\n                {\n                  type: 'input_text',\n                  text: message.content\n                }\n              ]\n            }\n          });\n          // Trigger response\n          this.sendToOpenAI({\n            type: 'response.create'\n          });\n          break;\n\n        default:\n          console.log('Unhandled client message type in proxy:', message.type);\n      }\n    } catch (error) {\n      console.error('Error handling client message in proxy:', error);\n    }\n  }\n\n  // Add a method to generate test audio for debugging\n  public generateTestAudio(testPhrase: string = \"Hello, I am testing the voice system. Can you hear me?\"): void {\n    if (!this.openaiWs || !this.isConnected) {\n      console.warn('Cannot generate test audio: not connected to OpenAI');\n      return;\n    }\n\n    console.log('🎤 Generating test audio for debugging...');\n    \n    // Create a conversation item with the test phrase\n    this.sendToOpenAI({\n      type: 'conversation.item.create',\n      item: {\n        type: 'message',\n        role: 'user',\n        content: [\n          {\n            type: 'input_text',\n            text: testPhrase\n          }\n        ]\n      }\n    });\n    \n    // Trigger response\n    this.sendToOpenAI({\n      type: 'response.create'\n    });\n  }\n\n  // Add a method to test round-trip audio\n  public testRoundTripAudio(): void {\n    console.log('🔄 Starting round-trip audio test...');\n    \n    // Generate a test phrase that we can then try to \"speak\" back\n    this.generateTestAudio(\"Please say hello back to me when you're ready to test the voice system.\");\n  }\n} ", "modifiedCode": "import { WebSocket } from 'ws';\nimport { EventEmitter } from 'events';\n\ninterface RealtimeProxyConfig {\n  apiKey: string;\n  model: string;\n  voice?: string;\n  instructions?: string;\n  temperature?: number;\n}\n\nexport class OpenAIRealtimeProxy extends EventEmitter {\n  private openaiWs: WebSocket | null = null;\n  private clientWs: WebSocket | null = null;\n  private config: RealtimeProxyConfig;\n  private isConnected = false;\n  private sessionId: string | null = null;\n  private isDisconnecting = false;\n  public onTranscriptUpdate?: (speaker: 'user' | 'ai', text: string) => void;\n  private currentAITranscript = '';\n  private currentUserTranscript = '';\n  private initialGreetingSent = false;\n  private hasActiveResponse = false;\n\n  constructor(config: RealtimeProxyConfig) {\n    super();\n    this.config = config;\n  }\n\n  async connect(clientWebSocket: WebSocket): Promise<void> {\n    this.clientWs = clientWebSocket;\n\n    try {\n      // Connect to OpenAI's Realtime API\n      const openaiUrl = `wss://api.openai.com/v1/realtime?model=${this.config.model}`;\n      \n      this.openaiWs = new WebSocket(openaiUrl, {\n        headers: {\n          'Authorization': `Bearer ${this.config.apiKey}`,\n          'OpenAI-Beta': 'realtime=v1'\n        }\n      });\n\n      this.setupOpenAIEventHandlers();\n      // Don't set up client event handlers here - let the main handler route messages\n\n      // Wait for OpenAI connection\n      await new Promise<void>((resolve, reject) => {\n        this.openaiWs!.once('open', () => {\n          console.log('Connected to OpenAI Realtime API');\n          this.isConnected = true;\n          this.initializeSession();\n          resolve();\n        });\n\n        this.openaiWs!.once('error', (error) => {\n          console.error('Failed to connect to OpenAI Realtime API:', error);\n          reject(error);\n        });\n      });\n\n    } catch (error) {\n      console.error('Error connecting to OpenAI Realtime API:', error);\n      throw error;\n    }\n  }\n\n  private setupOpenAIEventHandlers(): void {\n    if (!this.openaiWs) return;\n\n    this.openaiWs.on('message', (data) => {\n      try {\n        const message = JSON.parse(data.toString());\n        console.log('OpenAI → Client:', message.type);\n\n        // Handle specific message types\n        switch (message.type) {\n          case 'session.created':\n            this.sessionId = message.session.id;\n            console.log('OpenAI session created:', this.sessionId);\n            this.sendToClient(message); // Forward to client\n            break;\n          \n                  case 'session.updated':\n          console.log('OpenAI session updated');\n          \n          // Only trigger initial greeting once per session\n          if (!this.initialGreetingSent) {\n            this.initialGreetingSent = true;\n            \n            // Notify client that we're ready\n            this.sendToClient({\n              type: 'ready',\n              message: 'OpenAI Realtime session initialized'\n            });\n            \n            // Trigger initial AI greeting only once\n            console.log('Triggering initial AI greeting...');\n            this.sendToOpenAI({\n              type: 'conversation.item.create',\n              item: {\n                type: 'message',\n                role: 'system',\n                content: [\n                  {\n                    type: 'input_text',\n                    text: 'The user has just started a voice therapy session. Please greet them warmly and invite them to share what\\'s on their mind today. Keep it natural and conversational.'\n                  }\n                ]\n              }\n            });\n            \n            // Create response to trigger AI to speak\n            this.sendToOpenAI({\n              type: 'response.create'\n            });\n          } else {\n            console.log('Session configuration updated (VAD switch) - no new greeting needed');\n          }\n          \n          // Always forward session.updated to client\n          this.sendToClient(message);\n          break;\n\n          case 'conversation.item.input_audio_transcription.completed':\n            // User speech transcription - capture for summary\n            if (message.transcript && this.onTranscriptUpdate) {\n              console.log('Capturing user transcript:', message.transcript);\n              this.onTranscriptUpdate('user', message.transcript);\n            }\n            this.sendToClient(message);\n            break;\n            \n          case 'response.audio_transcript.delta':\n            // Accumulate AI transcript\n            if (message.delta) {\n              this.currentAITranscript += message.delta;\n            }\n            this.sendToClient(message);\n            break;\n            \n          case 'response.audio_transcript.done':\n            // AI speech transcription complete - capture for summary  \n            if (this.currentAITranscript && this.onTranscriptUpdate) {\n              console.log('Capturing AI transcript:', this.currentAITranscript);\n              this.onTranscriptUpdate('ai', this.currentAITranscript);\n              this.currentAITranscript = ''; // Reset for next response\n            }\n            this.sendToClient(message);\n            break;\n\n          case 'input_audio_buffer.speech_started':\n          case 'input_audio_buffer.speech_stopped':\n          case 'conversation.item.created':\n          case 'response.created':\n            // Track that we have an active response\n            this.hasActiveResponse = true;\n            this.sendToClient(message);\n            break;\n\n          case 'response.output_item.added':\n          case 'response.content_part.added':\n          case 'response.audio.delta':\n          case 'response.audio.done':\n          case 'response.text.delta':\n          case 'response.text.done':\n          case 'response.output_item.done':\n            // Forward these events directly to the client\n            this.sendToClient(message);\n            break;\n\n          case 'response.done':\n            // Response is complete, clear the active flag\n            this.hasActiveResponse = false;\n            this.sendToClient(message);\n            break;\n\n          case 'rate_limits.updated':\n            // Rate limit information - just log and forward\n            console.log('OpenAI rate limits updated');\n            this.sendToClient(message);\n            break;\n            \n          case 'response.content_part.done':\n            // Content part completion - forward to client\n            this.sendToClient(message);\n            break;\n\n          case 'error':\n            console.error('OpenAI API error:', message);\n            this.sendToClient({\n              type: 'error',\n              message: message.error?.message || 'OpenAI API error'\n            });\n            break;\n\n          default:\n            console.log('Unhandled OpenAI message type:', message.type);\n            // Forward unknown messages to client for debugging\n            this.sendToClient(message);\n        }\n      } catch (error) {\n        console.error('Error parsing OpenAI message:', error);\n      }\n    });\n\n    this.openaiWs.on('close', (code, reason) => {\n      console.log('OpenAI connection closed:', code, reason.toString());\n      this.isConnected = false;\n      \n      // Only send error message if this wasn't an intentional disconnect\n      if (!this.isDisconnecting) {\n        this.sendToClient({\n          type: 'error',\n          message: 'OpenAI connection closed'\n        });\n      }\n    });\n\n    this.openaiWs.on('error', (error) => {\n      console.error('OpenAI WebSocket error:', error);\n      this.sendToClient({\n        type: 'error',\n        message: 'OpenAI connection error'\n      });\n    });\n  }\n\n  private initializeSession(): void {\n    if (!this.openaiWs || !this.isConnected) return;\n\n    // Configure session with manual turn detection (client-side VAD)\n    const sessionConfig = {\n      type: 'session.update',\n      session: {\n        modalities: ['text', 'audio'],\n        instructions: this.config.instructions || 'You are Vale, an empathetic AI therapeutic assistant. Respond thoughtfully and supportively to help users process their emotions and thoughts.',\n        voice: this.config.voice || 'shimmer',\n        input_audio_format: 'pcm16',\n        output_audio_format: 'pcm16',\n        input_audio_transcription: {\n          model: 'whisper-1'\n        },\n        turn_detection: null, // Disable automatic turn detection - use client-side VAD\n        temperature: this.config.temperature || 0.7,\n        max_response_output_tokens: 1000\n      }\n    };\n\n    console.log('🎯 Configuring session with CLIENT-SIDE VAD (server VAD disabled)...');\n    console.log('📊 Turn Detection: DISABLED (manual commits from client)');\n    this.sendToOpenAI(sessionConfig);\n\n    console.log('✅ Client-side VAD configured - speech detection handled by browser');\n  }\n\n  private sendToOpenAI(message: any): void {\n    if (this.openaiWs && this.isConnected) {\n      this.openaiWs.send(JSON.stringify(message));\n    } else {\n      console.warn('Cannot send to OpenAI: not connected');\n    }\n  }\n\n  private sendToClient(message: any): void {\n    if (this.clientWs && this.clientWs.readyState === WebSocket.OPEN) {\n      this.clientWs.send(JSON.stringify(message));\n    }\n  }\n\n  private convertAudioFormat(audioData: number[]): string {\n    // Convert Int16Array audio data to base64 PCM16\n    const int16Array = new Int16Array(audioData);\n    const buffer = Buffer.from(int16Array.buffer);\n    return buffer.toString('base64');\n  }\n\n  private convertAudioArrayToBase64(audioData: number[], format?: string): string {\n    if (format === 'int16') {\n      // Already in correct format\n      const int16Array = new Int16Array(audioData);\n      const buffer = Buffer.from(int16Array.buffer);\n      return buffer.toString('base64');\n    } else {\n      // Assume float32, convert to int16\n      const int16Array = new Int16Array(audioData.length);\n      for (let i = 0; i < audioData.length; i++) {\n        const sample = Math.max(-1, Math.min(1, audioData[i]));\n        int16Array[i] = Math.floor(sample < 0 ? sample * 32768 : sample * 32767);\n      }\n      const buffer = Buffer.from(int16Array.buffer);\n      return buffer.toString('base64');\n    }\n  }\n\n  public disconnect(): void {\n    this.isDisconnecting = true;\n    \n    if (this.openaiWs) {\n      this.openaiWs.close();\n      this.openaiWs = null;\n    }\n    this.isConnected = false;\n    this.sessionId = null;\n  }\n\n  public getSessionId(): string | null {\n    return this.sessionId;\n  }\n\n  public isSessionActive(): boolean {\n    return this.isConnected && this.sessionId !== null;\n  }\n  \n  // Handle messages routed from the main websocket handler\n  public handleClientMessage(message: any): void {\n    try {\n      console.log('Realtime proxy handling message:', message.type);\n\n      // Handle client messages\n      switch (message.type) {\n        case 'start':\n          // Client wants to start - we already initialized, just confirm\n          this.sendToClient({\n            type: 'ready',\n            message: 'Session already active'\n          });\n          break;\n\n        case 'generate_test_audio':\n          // Generate test audio for debugging\n          this.generateTestAudio(message.text || \"This is a test of the voice system. Please respond when ready.\");\n          break;\n\n        case 'test_round_trip':\n          // Test complete round-trip audio flow\n          this.testRoundTripAudio();\n          break;\n\n        case 'input_audio_buffer.append':\n          // Forward audio data to OpenAI with enhanced debugging\n          const audioData = message.audio_data || message.audio;\n          if (audioData) {\n            console.log(`📤 Forwarding audio to OpenAI: ${typeof audioData} (${typeof audioData === 'string' ? audioData.length + ' chars' : 'non-string'})`);\n            \n            // Enhanced audio validation and debugging\n            if (typeof audioData === 'string' && audioData.length > 0) {\n              try {\n                const decoded = Buffer.from(audioData, 'base64');\n                const samples = decoded.length / 2; // PCM16 = 2 bytes per sample\n                console.log(`📊 Audio info: ${decoded.length} bytes, ${samples} samples`);\n                \n                // Analyze audio content for speech-like characteristics\n                const int16Array = new Int16Array(decoded.buffer);\n                let energy = 0;\n                let peakCount = 0;\n                let avgAmplitude = 0;\n                \n                for (let i = 0; i < int16Array.length; i++) {\n                  const sample = Math.abs(int16Array[i]);\n                  energy += sample;\n                  avgAmplitude += sample;\n                  \n                  // Count peaks (potential speech indicators)\n                  if (sample > 1000) {\n                    peakCount++;\n                  }\n                }\n                \n                const avgEnergy = energy / int16Array.length;\n                avgAmplitude = avgAmplitude / int16Array.length;\n                const peakRatio = peakCount / int16Array.length;\n                \n                console.log(`🔊 Audio analysis: avg=${avgEnergy.toFixed(2)}, amp=${avgAmplitude.toFixed(2)}, peaks=${(peakRatio*100).toFixed(2)}%`);\n                \n                // Determine if this looks like speech\n                const looksLikeSpeech = avgEnergy > 100 && peakRatio > 0.01;\n                console.log(`🎯 Speech likelihood: ${looksLikeSpeech ? 'HIGH' : 'LOW'} (energy=${avgEnergy > 100}, peaks=${peakRatio > 0.01})`);\n                \n              } catch (e) {\n                console.warn('Failed to analyze audio data:', e instanceof Error ? e.message : 'Unknown error');\n              }\n            }\n            \n            this.sendToOpenAI({\n              type: 'input_audio_buffer.append',\n              audio: message.audio_data ? this.convertAudioFormat(message.audio_data) : message.audio\n            });\n          } else {\n            console.warn('No audio data in input_audio_buffer.append message');\n          }\n          break;\n\n        case 'input_audio_buffer.commit':\n          // Forward commit to OpenAI\n          this.sendToOpenAI(message);\n          \n          // Since we're using manual VAD, automatically create a response after commit\n          setTimeout(() => {\n            if (this.openaiWs && this.isConnected) {\n              console.log('🤖 Auto-creating response after manual audio commit');\n              this.sendToOpenAI({\n                type: 'response.create'\n              });\n            }\n          }, 100); // Small delay to ensure commit is processed\n          break;\n\n        case 'response.create':\n        case 'response.cancel':\n        case 'conversation.item.create':\n        case 'conversation.item.delete':\n        case 'conversation.item.truncate':\n          // Forward these commands directly to OpenAI\n          this.sendToOpenAI(message);\n          break;\n\n        case 'audio_chunk':\n          // Convert legacy audio_chunk to OpenAI format\n          if (message.audio_data && Array.isArray(message.audio_data)) {\n            const base64Audio = this.convertAudioArrayToBase64(message.audio_data, message.format);\n            this.sendToOpenAI({\n              type: 'input_audio_buffer.append',\n              audio: base64Audio\n            });\n          }\n          break;\n\n        case 'text':\n          // Convert text message to OpenAI conversation item\n          this.sendToOpenAI({\n            type: 'conversation.item.create',\n            item: {\n              type: 'message',\n              role: 'user',\n              content: [\n                {\n                  type: 'input_text',\n                  text: message.content\n                }\n              ]\n            }\n          });\n          // Trigger response\n          this.sendToOpenAI({\n            type: 'response.create'\n          });\n          break;\n\n        default:\n          console.log('Unhandled client message type in proxy:', message.type);\n      }\n    } catch (error) {\n      console.error('Error handling client message in proxy:', error);\n    }\n  }\n\n  // Add a method to generate test audio for debugging\n  public generateTestAudio(testPhrase: string = \"Hello, I am testing the voice system. Can you hear me?\"): void {\n    if (!this.openaiWs || !this.isConnected) {\n      console.warn('Cannot generate test audio: not connected to OpenAI');\n      return;\n    }\n\n    console.log('🎤 Generating test audio for debugging...');\n    \n    // Create a conversation item with the test phrase\n    this.sendToOpenAI({\n      type: 'conversation.item.create',\n      item: {\n        type: 'message',\n        role: 'user',\n        content: [\n          {\n            type: 'input_text',\n            text: testPhrase\n          }\n        ]\n      }\n    });\n    \n    // Trigger response\n    this.sendToOpenAI({\n      type: 'response.create'\n    });\n  }\n\n  // Add a method to test round-trip audio\n  public testRoundTripAudio(): void {\n    console.log('🔄 Starting round-trip audio test...');\n    \n    // Generate a test phrase that we can then try to \"speak\" back\n    this.generateTestAudio(\"Please say hello back to me when you're ready to test the voice system.\");\n  }\n} "}