{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "test-full-conversation.js"}, "originalCode": "#!/usr/bin/env node\n\nimport { WebSocket } from 'ws';\n\nconst WEBSOCKET_URL = 'ws://localhost:5000/ws';\n\nconsole.log('🗣️ Testing Full Real-time Conversation...');\nconsole.log('📡 Connecting to:', WEBSOCKET_URL);\n\nconst ws = new WebSocket(WEBSOCKET_URL);\n\nlet messageCount = 0;\nlet conversationTurns = 0;\nconst maxTurns = 3; // Test 3 conversation turns\n\n// Generate realistic speech-like audio data\nfunction generateSpeechAudio(durationMs = 2000, speechIntensity = 0.3) {\n  const sampleRate = 24000;\n  const samples = Math.floor(sampleRate * durationMs / 1000);\n  const audioBuffer = new Int16Array(samples);\n  \n  // Generate more realistic speech-like patterns\n  for (let i = 0; i < samples; i++) {\n    // Create speech-like patterns with varying amplitude\n    const time = i / sampleRate;\n    const frequency = 200 + Math.sin(time * 10) * 50; // Varying frequency\n    const amplitude = speechIntensity * (0.5 + 0.5 * Math.sin(time * 3)); // Varying amplitude\n    \n    // Add some noise and harmonics to make it more speech-like\n    const signal = Math.sin(2 * Math.PI * frequency * time) * amplitude;\n    const noise = (Math.random() - 0.5) * 0.1 * amplitude;\n    const harmonic = Math.sin(2 * Math.PI * frequency * 2 * time) * amplitude * 0.3;\n    \n    audioBuffer[i] = Math.floor((signal + noise + harmonic) * 16000);\n  }\n  \n  const buffer = Buffer.from(audioBuffer.buffer);\n  return buffer.toString('base64');\n}\n\nws.on('open', () => {\n  console.log('✅ WebSocket connected');\n  \n  const startMessage = {\n    type: \"start\",\n    userId: \"test-user\",\n    clientId: \"test-client\",\n    useRealtimeAPI: true,\n    mode: \"realtime\",\n    behavior: {\n      model: \"gpt-4o-realtime-preview\",\n      temperature: 0.7,\n      voice: {\n        voice: \"shimmer\",\n        speed: 1.0\n      }\n    },\n    instructions: \"You are Vale, an empathetic AI therapeutic assistant. Keep responses very brief (1-2 sentences) for this test. Ask simple questions to keep the conversation flowing.\"\n  };\n  \n  console.log('📤 Starting conversation...');\n  ws.send(JSON.stringify(startMessage));\n});\n\nws.on('message', (data) => {\n  messageCount++;\n  try {\n    const message = JSON.parse(data.toString());\n    \n    // Only log important events to reduce noise\n    if (['ready', 'response.audio_transcript.done', 'response.done', 'conversation.item.input_audio_transcription.completed', 'error'].includes(message.type)) {\n      console.log(`📥 ${message.type}`);\n    }\n    \n    switch (message.type) {\n      case 'ready':\n        console.log('✅ Session ready! Waiting for AI greeting...');\n        break;\n        \n      case 'response.audio_transcript.done':\n        console.log(`🤖 AI: \"${message.transcript}\"`);\n        break;\n        \n      case 'response.done':\n        conversationTurns++;\n        console.log(`✅ AI response ${conversationTurns} completed`);\n        \n        if (conversationTurns < maxTurns) {\n          // Send user response after a short delay\n          setTimeout(() => {\n            console.log(`🎤 Sending user response ${conversationTurns}...`);\n            \n            // Generate speech-like audio\n            const audioData = generateSpeechAudio(1500, 0.4); // 1.5 seconds of speech\n            \n            // Send audio in chunks to simulate real streaming\n            const chunkSize = 8000; // 8KB chunks\n            const chunks = [];\n            for (let i = 0; i < audioData.length; i += chunkSize) {\n              chunks.push(audioData.slice(i, i + chunkSize));\n            }\n            \n            console.log(`📤 Sending ${chunks.length} audio chunks...`);\n            \n            // Send chunks with small delays\n            chunks.forEach((chunk, index) => {\n              setTimeout(() => {\n                ws.send(JSON.stringify({\n                  type: 'input_audio_buffer.append',\n                  audio: chunk\n                }));\n                \n                // Commit after the last chunk\n                if (index === chunks.length - 1) {\n                  setTimeout(() => {\n                    console.log('📤 Committing audio...');\n                    ws.send(JSON.stringify({\n                      type: 'input_audio_buffer.commit'\n                    }));\n                  }, 50);\n                }\n              }, index * 20); // 20ms between chunks\n            });\n            \n          }, 1000); // Wait 1 second before responding\n        } else {\n          // End conversation after max turns\n          setTimeout(() => {\n            console.log('📤 Ending conversation...');\n            ws.send(JSON.stringify({\n              type: 'conversationEnded'\n            }));\n          }, 1000);\n        }\n        break;\n        \n      case 'conversation.item.input_audio_transcription.completed':\n        console.log(`🎯 User speech transcribed: \"${message.transcript}\"`);\n        break;\n        \n      case 'end_ack':\n        console.log('✅ Conversation ended successfully');\n        \n        setTimeout(() => {\n          console.log('\\n=== CONVERSATION TEST SUMMARY ===');\n          console.log(`✅ Completed ${conversationTurns} conversation turns`);\n          console.log(`📊 Total messages processed: ${messageCount}`);\n          console.log('🎉 Real-time conversation test PASSED!');\n          ws.close();\n        }, 500);\n        break;\n        \n      case 'error':\n        console.error('❌ Error:', message.message);\n        break;\n    }\n  } catch (error) {\n    console.error('❌ Error parsing message:', error);\n  }\n});\n\nws.on('close', (code, reason) => {\n  console.log(`🔌 Connection closed: ${code} ${reason.toString()}`);\n});\n\nws.on('error', (error) => {\n  console.error('❌ WebSocket error:', error);\n});\n\n// Timeout to prevent hanging\nsetTimeout(() => {\n  console.log('⏰ Test timeout - conversation test failed');\n  ws.close();\n  process.exit(1);\n}, 60000); // 60 second timeout\n", "modifiedCode": "#!/usr/bin/env node\n\nimport { WebSocket } from 'ws';\n\nconst WEBSOCKET_URL = 'ws://localhost:5000/ws';\n\nconsole.log('🗣️ Testing Full Real-time Conversation...');\nconsole.log('📡 Connecting to:', WEBSOCKET_URL);\n\nconst ws = new WebSocket(WEBSOCKET_URL);\n\nlet messageCount = 0;\nlet conversationTurns = 0;\nconst maxTurns = 3; // Test 3 conversation turns\n\n// Generate realistic speech-like audio data\nfunction generateSpeechAudio(durationMs = 2000, speechIntensity = 0.3) {\n  const sampleRate = 24000;\n  const samples = Math.floor(sampleRate * durationMs / 1000);\n  const audioBuffer = new Int16Array(samples);\n  \n  // Generate more realistic speech-like patterns\n  for (let i = 0; i < samples; i++) {\n    // Create speech-like patterns with varying amplitude\n    const time = i / sampleRate;\n    const frequency = 200 + Math.sin(time * 10) * 50; // Varying frequency\n    const amplitude = speechIntensity * (0.5 + 0.5 * Math.sin(time * 3)); // Varying amplitude\n    \n    // Add some noise and harmonics to make it more speech-like\n    const signal = Math.sin(2 * Math.PI * frequency * time) * amplitude;\n    const noise = (Math.random() - 0.5) * 0.1 * amplitude;\n    const harmonic = Math.sin(2 * Math.PI * frequency * 2 * time) * amplitude * 0.3;\n    \n    audioBuffer[i] = Math.floor((signal + noise + harmonic) * 16000);\n  }\n  \n  const buffer = Buffer.from(audioBuffer.buffer);\n  return buffer.toString('base64');\n}\n\nws.on('open', () => {\n  console.log('✅ WebSocket connected');\n  \n  const startMessage = {\n    type: \"start\",\n    userId: \"test-user\",\n    clientId: \"test-client\",\n    useRealtimeAPI: true,\n    mode: \"realtime\",\n    behavior: {\n      model: \"gpt-4o-realtime-preview-2025-06-03\",\n      temperature: 0.7,\n      voice: {\n        voice: \"shimmer\",\n        speed: 1.0\n      }\n    },\n    instructions: \"You are Vale, an empathetic AI therapeutic assistant. Keep responses very brief (1-2 sentences) for this test. Ask simple questions to keep the conversation flowing.\"\n  };\n  \n  console.log('📤 Starting conversation...');\n  ws.send(JSON.stringify(startMessage));\n});\n\nws.on('message', (data) => {\n  messageCount++;\n  try {\n    const message = JSON.parse(data.toString());\n    \n    // Only log important events to reduce noise\n    if (['ready', 'response.audio_transcript.done', 'response.done', 'conversation.item.input_audio_transcription.completed', 'error'].includes(message.type)) {\n      console.log(`📥 ${message.type}`);\n    }\n    \n    switch (message.type) {\n      case 'ready':\n        console.log('✅ Session ready! Waiting for AI greeting...');\n        break;\n        \n      case 'response.audio_transcript.done':\n        console.log(`🤖 AI: \"${message.transcript}\"`);\n        break;\n        \n      case 'response.done':\n        conversationTurns++;\n        console.log(`✅ AI response ${conversationTurns} completed`);\n        \n        if (conversationTurns < maxTurns) {\n          // Send user response after a short delay\n          setTimeout(() => {\n            console.log(`🎤 Sending user response ${conversationTurns}...`);\n            \n            // Generate speech-like audio\n            const audioData = generateSpeechAudio(1500, 0.4); // 1.5 seconds of speech\n            \n            // Send audio in chunks to simulate real streaming\n            const chunkSize = 8000; // 8KB chunks\n            const chunks = [];\n            for (let i = 0; i < audioData.length; i += chunkSize) {\n              chunks.push(audioData.slice(i, i + chunkSize));\n            }\n            \n            console.log(`📤 Sending ${chunks.length} audio chunks...`);\n            \n            // Send chunks with small delays\n            chunks.forEach((chunk, index) => {\n              setTimeout(() => {\n                ws.send(JSON.stringify({\n                  type: 'input_audio_buffer.append',\n                  audio: chunk\n                }));\n                \n                // Commit after the last chunk\n                if (index === chunks.length - 1) {\n                  setTimeout(() => {\n                    console.log('📤 Committing audio...');\n                    ws.send(JSON.stringify({\n                      type: 'input_audio_buffer.commit'\n                    }));\n                  }, 50);\n                }\n              }, index * 20); // 20ms between chunks\n            });\n            \n          }, 1000); // Wait 1 second before responding\n        } else {\n          // End conversation after max turns\n          setTimeout(() => {\n            console.log('📤 Ending conversation...');\n            ws.send(JSON.stringify({\n              type: 'conversationEnded'\n            }));\n          }, 1000);\n        }\n        break;\n        \n      case 'conversation.item.input_audio_transcription.completed':\n        console.log(`🎯 User speech transcribed: \"${message.transcript}\"`);\n        break;\n        \n      case 'end_ack':\n        console.log('✅ Conversation ended successfully');\n        \n        setTimeout(() => {\n          console.log('\\n=== CONVERSATION TEST SUMMARY ===');\n          console.log(`✅ Completed ${conversationTurns} conversation turns`);\n          console.log(`📊 Total messages processed: ${messageCount}`);\n          console.log('🎉 Real-time conversation test PASSED!');\n          ws.close();\n        }, 500);\n        break;\n        \n      case 'error':\n        console.error('❌ Error:', message.message);\n        break;\n    }\n  } catch (error) {\n    console.error('❌ Error parsing message:', error);\n  }\n});\n\nws.on('close', (code, reason) => {\n  console.log(`🔌 Connection closed: ${code} ${reason.toString()}`);\n});\n\nws.on('error', (error) => {\n  console.error('❌ WebSocket error:', error);\n});\n\n// Timeout to prevent hanging\nsetTimeout(() => {\n  console.log('⏰ Test timeout - conversation test failed');\n  ws.close();\n  process.exit(1);\n}, 60000); // 60 second timeout\n"}