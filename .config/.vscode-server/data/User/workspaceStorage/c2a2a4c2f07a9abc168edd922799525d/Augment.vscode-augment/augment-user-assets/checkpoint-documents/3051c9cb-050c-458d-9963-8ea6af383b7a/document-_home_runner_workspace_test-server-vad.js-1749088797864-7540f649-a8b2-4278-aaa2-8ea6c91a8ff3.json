{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "test-server-vad.js"}, "modifiedCode": "#!/usr/bin/env node\n\nimport { WebSocket } from 'ws';\n\nconst WEBSOCKET_URL = 'ws://localhost:5000/ws';\n\nconsole.log('🎯 Testing Server-Side VAD Configuration...');\nconsole.log('📡 Connecting to:', WEBSOCKET_URL);\n\nconst ws = new WebSocket(WEBSOCKET_URL);\n\nlet messageCount = 0;\nconst receivedMessages = [];\n\nws.on('open', () => {\n  console.log('✅ WebSocket connected');\n  \n  // Send start message with new model\n  const startMessage = {\n    type: \"start\",\n    userId: \"vad-test\",\n    clientId: \"vad-test\",\n    useRealtimeAPI: true,\n    mode: \"realtime\",\n    behavior: {\n      model: \"gpt-4o-realtime-preview-2025-06-03\",\n      temperature: 0.7,\n      voice: {\n        voice: \"shimmer\",\n        speed: 1.0\n      }\n    },\n    instructions: \"You are <PERSON>, an empathetic AI therapeutic assistant. Keep responses brief for testing.\"\n  };\n  \n  console.log('📤 Starting session with new model and server-side VAD...');\n  ws.send(JSON.stringify(startMessage));\n});\n\nws.on('message', (data) => {\n  messageCount++;\n  try {\n    const message = JSON.parse(data.toString());\n    console.log(`📥 Message ${messageCount}: ${message.type}`);\n    receivedMessages.push(message.type);\n    \n    // Handle specific message types\n    switch (message.type) {\n      case 'session.created':\n        console.log('✅ Session created with session ID:', message.session?.id);\n        break;\n        \n      case 'session.updated':\n        console.log('✅ Session updated - VAD configuration applied');\n        if (message.session?.turn_detection) {\n          console.log('🎯 Turn detection config:', JSON.stringify(message.session.turn_detection, null, 2));\n        } else {\n          console.log('⚠️ Turn detection is null - using manual mode');\n        }\n        break;\n        \n      case 'ready':\n        console.log('✅ Session ready! Testing continuous audio streaming...');\n        \n        // Start streaming audio continuously after a short delay\n        setTimeout(() => {\n          console.log('🎤 Starting continuous audio stream...');\n          streamContinuousAudio();\n        }, 1000);\n        break;\n        \n      case 'input_audio_buffer.speech_started':\n        console.log('🎤 SERVER VAD: Speech started detected by OpenAI!');\n        break;\n        \n      case 'input_audio_buffer.speech_stopped':\n        console.log('🔇 SERVER VAD: Speech stopped detected by OpenAI!');\n        break;\n        \n      case 'conversation.item.input_audio_transcription.completed':\n        console.log(`🎯 User speech transcribed: \"${message.transcript}\"`);\n        break;\n        \n      case 'response.audio_transcript.done':\n        console.log(`🤖 AI response: \"${message.transcript}\"`);\n        break;\n        \n      case 'response.done':\n        console.log('✅ AI response completed');\n        \n        // End test after first response\n        setTimeout(() => {\n          console.log('📤 Ending test...');\n          ws.send(JSON.stringify({\n            type: 'conversationEnded'\n          }));\n        }, 1000);\n        break;\n        \n      case 'end_ack':\n        console.log('✅ Test completed successfully');\n        \n        setTimeout(() => {\n          console.log('\\n=== SERVER VAD TEST SUMMARY ===');\n          console.log('Received messages:', receivedMessages);\n          console.log('✅ Server-side VAD test completed');\n          ws.close();\n        }, 500);\n        break;\n        \n      case 'error':\n        console.error('❌ Error:', message.message);\n        if (message.error) {\n          console.error('Error details:', message.error);\n        }\n        break;\n    }\n  } catch (error) {\n    console.error('❌ Error parsing message:', error);\n  }\n});\n\n// Function to stream continuous audio (simulating real microphone input)\nfunction streamContinuousAudio() {\n  let chunkCount = 0;\n  const maxChunks = 100; // Stream for about 4 seconds\n  \n  const streamInterval = setInterval(() => {\n    chunkCount++;\n    \n    // Generate realistic speech-like audio\n    const sampleRate = 24000;\n    const chunkDurationMs = 40; // 40ms chunks\n    const samples = Math.floor(sampleRate * chunkDurationMs / 1000);\n    const audioBuffer = new Int16Array(samples);\n    \n    // Create speech-like patterns with varying intensity\n    const speechIntensity = chunkCount > 20 && chunkCount < 60 ? 0.4 : 0.05; // Speech in middle\n    \n    for (let i = 0; i < samples; i++) {\n      const time = (chunkCount * chunkDurationMs / 1000) + (i / sampleRate);\n      const frequency = 200 + Math.sin(time * 10) * 50;\n      const amplitude = speechIntensity * (0.5 + 0.5 * Math.sin(time * 3));\n      \n      const signal = Math.sin(2 * Math.PI * frequency * time) * amplitude;\n      const noise = (Math.random() - 0.5) * 0.1 * amplitude;\n      \n      audioBuffer[i] = Math.floor((signal + noise) * 16000);\n    }\n    \n    // Convert to base64\n    const buffer = Buffer.from(audioBuffer.buffer);\n    const base64Audio = buffer.toString('base64');\n    \n    // Send audio chunk\n    ws.send(JSON.stringify({\n      type: 'input_audio_buffer.append',\n      audio: base64Audio\n    }));\n    \n    // Log progress\n    if (chunkCount % 25 === 0) {\n      console.log(`📤 Streamed ${chunkCount} audio chunks (${speechIntensity > 0.1 ? 'SPEECH' : 'silence'})`);\n    }\n    \n    // Stop streaming after max chunks\n    if (chunkCount >= maxChunks) {\n      clearInterval(streamInterval);\n      console.log('🔇 Audio streaming completed');\n    }\n  }, 40); // Send chunk every 40ms\n}\n\nws.on('close', (code, reason) => {\n  console.log(`🔌 Connection closed: ${code} ${reason.toString()}`);\n});\n\nws.on('error', (error) => {\n  console.error('❌ WebSocket error:', error);\n});\n\n// Timeout to prevent hanging\nsetTimeout(() => {\n  console.log('⏰ Test timeout');\n  ws.close();\n  process.exit(0);\n}, 30000); // 30 second timeout\n"}