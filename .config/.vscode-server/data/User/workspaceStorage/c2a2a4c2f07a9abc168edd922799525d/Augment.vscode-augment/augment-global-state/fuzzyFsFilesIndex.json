{"/home/<USER>/workspace/.config/.vscode-server/data/logs/20250605T012718/exthost1/Augment.vscode-augment/Augment.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250605T012718/exthost1/Augment.vscode-augment/Augment.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-global-state/fuzzyFsFilesIndex.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-global-state/fuzzyFsFilesIndex.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/task-storage/tasks/e63218cb-f243-4533-a019-7f5c04682545": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/task-storage/tasks/e63218cb-f243-4533-a019-7f5c04682545"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/vscode.lock": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/vscode.lock"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/task-storage/tasks/f696643d-f727-4e88-97b6-598700341fc9": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/task-storage/tasks/f696643d-f727-4e88-97b6-598700341fc9"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250605T012718/exthost1/vscode.git/Git.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250605T012718/exthost1/vscode.git/Git.log"}, "/home/<USER>/workspace/.gitignore": {"rootPath": "/home/<USER>/workspace", "relPath": ".giti<PERSON>re"}, "/home/<USER>/workspace/components.json": {"rootPath": "/home/<USER>/workspace", "relPath": "components.json"}, "/home/<USER>/workspace/drizzle.config.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "drizzle.config.ts"}, "/home/<USER>/workspace/postcss.config.js": {"rootPath": "/home/<USER>/workspace", "relPath": "postcss.config.js"}, "/home/<USER>/workspace/tailwind.config.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "tailwind.config.ts"}, "/home/<USER>/workspace/tsconfig.json": {"rootPath": "/home/<USER>/workspace", "relPath": "tsconfig.json"}, "/home/<USER>/workspace/vite.config.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "vite.config.ts"}, "/home/<USER>/workspace/client/src/pages/not-found.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/not-found.tsx"}, "/home/<USER>/workspace/client/src/lib/queryClient.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/queryClient.ts"}, "/home/<USER>/workspace/client/src/hooks/use-mobile.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/use-mobile.tsx"}, "/home/<USER>/workspace/client/src/hooks/use-toast.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/use-toast.ts"}, "/home/<USER>/workspace/client/src/components/ui/accordion.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/accordion.tsx"}, "/home/<USER>/workspace/client/src/components/ui/alert-dialog.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/alert-dialog.tsx"}, "/home/<USER>/workspace/client/src/components/ui/alert.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/alert.tsx"}, "/home/<USER>/workspace/client/src/components/ui/aspect-ratio.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/aspect-ratio.tsx"}, "/home/<USER>/workspace/client/src/components/ui/avatar.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/avatar.tsx"}, "/home/<USER>/workspace/client/src/components/ui/breadcrumb.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/breadcrumb.tsx"}, "/home/<USER>/workspace/client/src/components/ui/button.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/button.tsx"}, "/home/<USER>/workspace/client/src/components/ui/calendar.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/calendar.tsx"}, "/home/<USER>/workspace/client/src/components/ui/card.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/card.tsx"}, "/home/<USER>/workspace/client/src/components/ui/carousel.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/carousel.tsx"}, "/home/<USER>/workspace/client/src/components/ui/chart.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/chart.tsx"}, "/home/<USER>/workspace/client/src/components/ui/checkbox.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/checkbox.tsx"}, "/home/<USER>/workspace/client/src/components/ui/collapsible.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/collapsible.tsx"}, "/home/<USER>/workspace/client/src/components/ui/command.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/command.tsx"}, "/home/<USER>/workspace/client/src/components/ui/context-menu.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/context-menu.tsx"}, "/home/<USER>/workspace/client/src/components/ui/dialog.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/dialog.tsx"}, "/home/<USER>/workspace/client/src/components/ui/dropdown-menu.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/dropdown-menu.tsx"}, "/home/<USER>/workspace/client/src/components/ui/form.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/form.tsx"}, "/home/<USER>/workspace/client/src/components/ui/hover-card.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/hover-card.tsx"}, "/home/<USER>/workspace/client/src/components/ui/input-otp.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/input-otp.tsx"}, "/home/<USER>/workspace/client/src/components/ui/input.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/input.tsx"}, "/home/<USER>/workspace/client/src/components/ui/label.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/label.tsx"}, "/home/<USER>/workspace/client/src/components/ui/menubar.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/menubar.tsx"}, "/home/<USER>/workspace/client/src/components/ui/navigation-menu.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/navigation-menu.tsx"}, "/home/<USER>/workspace/client/src/components/ui/pagination.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/pagination.tsx"}, "/home/<USER>/workspace/client/src/components/ui/popover.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/popover.tsx"}, "/home/<USER>/workspace/client/src/components/ui/progress.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/progress.tsx"}, "/home/<USER>/workspace/client/src/components/ui/radio-group.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/radio-group.tsx"}, "/home/<USER>/workspace/client/src/components/ui/resizable.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/resizable.tsx"}, "/home/<USER>/workspace/client/src/components/ui/scroll-area.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/scroll-area.tsx"}, "/home/<USER>/workspace/client/src/components/ui/select.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/select.tsx"}, "/home/<USER>/workspace/client/src/components/ui/separator.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/separator.tsx"}, "/home/<USER>/workspace/client/src/components/ui/sheet.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/sheet.tsx"}, "/home/<USER>/workspace/client/src/components/ui/sidebar.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/sidebar.tsx"}, "/home/<USER>/workspace/client/src/components/ui/skeleton.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/skeleton.tsx"}, "/home/<USER>/workspace/client/src/components/ui/switch.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/switch.tsx"}, "/home/<USER>/workspace/client/src/components/ui/table.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/table.tsx"}, "/home/<USER>/workspace/client/src/components/ui/tabs.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/tabs.tsx"}, "/home/<USER>/workspace/client/src/components/ui/textarea.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/textarea.tsx"}, "/home/<USER>/workspace/client/src/components/ui/toast.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/toast.tsx"}, "/home/<USER>/workspace/client/src/components/ui/toaster.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/toaster.tsx"}, "/home/<USER>/workspace/client/src/components/ui/toggle-group.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/toggle-group.tsx"}, "/home/<USER>/workspace/client/src/components/ui/toggle.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/toggle.tsx"}, "/home/<USER>/workspace/client/src/components/ui/tooltip.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/tooltip.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/tunnel-forwarding/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/tunnel-forwarding/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/microsoft-authentication/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/microsoft-authentication/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/json-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/json-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/json-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/json-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/gulp/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/gulp/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/tunnel-forwarding/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/tunnel-forwarding/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/references-view/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/references-view/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/php-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/php-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/jake/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/jake/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/gulp/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/gulp/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/grunt/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/grunt/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-global-state/fuzzyFsFoldersIndex.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-global-state/fuzzyFsFoldersIndex.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/augment.vscode-augment/augment-global-state/terminalSettings.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/augment.vscode-augment/augment-global-state/terminalSettings.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/extension-editing/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/extension-editing/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/emmet/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/emmet/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-server-ready/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-server-ready/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-auto-launch/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-auto-launch/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/configuration-editing/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/configuration-editing/package.json"}, "/home/<USER>/workspace/.cache/typescript/5.7/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/typescript/5.7/package.json"}, "/home/<USER>/workspace/.replit": {"rootPath": "/home/<USER>/workspace", "relPath": ".replit"}, "/home/<USER>/workspace/ExpoGoExportGuide.md": {"rootPath": "/home/<USER>/workspace", "relPath": "ExpoGoExportGuide.md"}, "/home/<USER>/workspace/MobileOptimizationsGuide.md": {"rootPath": "/home/<USER>/workspace", "relPath": "MobileOptimizationsGuide.md"}, "/home/<USER>/workspace/admin-login-test.js": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-login-test.js"}, "/home/<USER>/workspace/audio-test-result.txt": {"rootPath": "/home/<USER>/workspace", "relPath": "audio-test-result.txt"}, "/home/<USER>/workspace/conversation-test-result.txt": {"rootPath": "/home/<USER>/workspace", "relPath": "conversation-test-result.txt"}, "/home/<USER>/workspace/cookies.txt": {"rootPath": "/home/<USER>/workspace", "relPath": "cookies.txt"}, "/home/<USER>/workspace/create-admin-user.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "create-admin-user.ts"}, "/home/<USER>/workspace/create-admin.cjs": {"rootPath": "/home/<USER>/workspace", "relPath": "create-admin.cjs"}, "/home/<USER>/workspace/debug-admin.js": {"rootPath": "/home/<USER>/workspace", "relPath": "debug-admin.js"}, "/home/<USER>/workspace/debug-auth.js": {"rootPath": "/home/<USER>/workspace", "relPath": "debug-auth.js"}, "/home/<USER>/workspace/fix-authentication.js": {"rootPath": "/home/<USER>/workspace", "relPath": "fix-authentication.js"}, "/home/<USER>/workspace/login-test.js": {"rootPath": "/home/<USER>/workspace", "relPath": "login-test.js"}, "/home/<USER>/workspace/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": "package.json"}, "/home/<USER>/workspace/test-advanced-vad.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test-advanced-vad.js"}, "/home/<USER>/workspace/test-audio-flow.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test-audio-flow.js"}, "/home/<USER>/workspace/test-audio-transcript-fix.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test-audio-transcript-fix.js"}, "/home/<USER>/workspace/test-auth.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test-auth.js"}, "/home/<USER>/workspace/test-complete-flow.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test-complete-flow.js"}, "/home/<USER>/workspace/test-error-handling-fix.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test-error-handling-fix.js"}, "/home/<USER>/workspace/test-greeting-fix.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test-greeting-fix.js"}, "/home/<USER>/workspace/test-handler-check.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test-handler-check.js"}, "/home/<USER>/workspace/test-login.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test-login.js"}, "/home/<USER>/workspace/test-manual-turn-detection.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test-manual-turn-detection.js"}, "/home/<USER>/workspace/test-microphone-debug.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test-microphone-debug.js"}, "/home/<USER>/workspace/test-microphone-fix.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test-microphone-fix.js"}, "/home/<USER>/workspace/test-output.log": {"rootPath": "/home/<USER>/workspace", "relPath": "test-output.log"}, "/home/<USER>/workspace/test-real-openai-audio.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test-real-openai-audio.js"}, "/home/<USER>/workspace/test-result.txt": {"rootPath": "/home/<USER>/workspace", "relPath": "test-result.txt"}, "/home/<USER>/workspace/test-speech-simulation.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test-speech-simulation.js"}, "/home/<USER>/workspace/test-websocket-flow.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test-websocket-flow.js"}, "/home/<USER>/workspace/uploads/audio/3e5b9f76-e311-46e6-8939-829a501014f9.webm": {"rootPath": "/home/<USER>/workspace", "relPath": "uploads/audio/3e5b9f76-e311-46e6-8939-829a501014f9.webm"}, "/home/<USER>/workspace/uploads/audio/e0199b7b-742e-4bf2-8d6f-b02f670e5794.webm": {"rootPath": "/home/<USER>/workspace", "relPath": "uploads/audio/e0199b7b-742e-4bf2-8d6f-b02f670e5794.webm"}, "/home/<USER>/workspace/shared/schema.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "shared/schema.ts"}, "/home/<USER>/workspace/server/api.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/api.ts"}, "/home/<USER>/workspace/server/audio-processor.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/audio-processor.ts"}, "/home/<USER>/workspace/server/auth.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/auth.ts"}, "/home/<USER>/workspace/server/custom-queries.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/custom-queries.ts"}, "/home/<USER>/workspace/server/db.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/db.ts"}, "/home/<USER>/workspace/server/enhanced-websocket-handler.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/enhanced-websocket-handler.ts"}, "/home/<USER>/workspace/server/routes.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes.ts"}, "/home/<USER>/workspace/server/routes.ts.bak": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes.ts.bak"}, "/home/<USER>/workspace/server/schema.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/schema.ts"}, "/home/<USER>/workspace/server/session-tables.js": {"rootPath": "/home/<USER>/workspace", "relPath": "server/session-tables.js"}, "/home/<USER>/workspace/server/storage.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/storage.ts"}, "/home/<USER>/workspace/server/summary-service.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/summary-service.ts"}, "/home/<USER>/workspace/server/temp-login-fix.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/temp-login-fix.ts"}, "/home/<USER>/workspace/server/types.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/types.ts"}, "/home/<USER>/workspace/server/vite.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/vite.ts"}, "/home/<USER>/workspace/server/websocket-handler.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/websocket-handler.ts"}, "/home/<USER>/workspace/server/tests/generate-test-audio.js": {"rootPath": "/home/<USER>/workspace", "relPath": "server/tests/generate-test-audio.js"}, "/home/<USER>/workspace/server/tests/run-test.js": {"rootPath": "/home/<USER>/workspace", "relPath": "server/tests/run-test.js"}, "/home/<USER>/workspace/server/tests/websocket-test.js": {"rootPath": "/home/<USER>/workspace", "relPath": "server/tests/websocket-test.js"}, "/home/<USER>/workspace/server/db/schema.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/db/schema.ts"}, "/home/<USER>/workspace/server/db/types.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/db/types.ts"}, "/home/<USER>/workspace/scripts/create-admin.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "scripts/create-admin.ts"}, "/home/<USER>/workspace/scripts/generate-test-audio.js": {"rootPath": "/home/<USER>/workspace", "relPath": "scripts/generate-test-audio.js"}, "/home/<USER>/workspace/scripts/push-schema.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "scripts/push-schema.ts"}, "/home/<USER>/workspace/scripts/setup-database.sh": {"rootPath": "/home/<USER>/workspace", "relPath": "scripts/setup-database.sh"}, "/home/<USER>/workspace/scripts/test-websocket.js": {"rootPath": "/home/<USER>/workspace", "relPath": "scripts/test-websocket.js"}, "/home/<USER>/workspace/pages/api/configs.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "pages/api/configs.ts"}, "/home/<USER>/workspace/pages/api/save-config.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "pages/api/save-config.ts"}, "/home/<USER>/workspace/pages/api/sessions/end.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "pages/api/sessions/end.ts"}, "/home/<USER>/workspace/pages/api/sessions/start.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "pages/api/sessions/start.ts"}, "/home/<USER>/workspace/pages/api/doctor/clients.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "pages/api/doctor/clients.ts"}, "/home/<USER>/workspace/pages/api/doctor/clients/[clientId]/sessions.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "pages/api/doctor/clients/[clientId]/sessions.ts"}, "/home/<USER>/workspace/migrations/0000_first_bishop.sql": {"rootPath": "/home/<USER>/workspace", "relPath": "migrations/0000_first_bishop.sql"}, "/home/<USER>/workspace/migrations/meta/0000_snapshot.json": {"rootPath": "/home/<USER>/workspace", "relPath": "migrations/meta/0000_snapshot.json"}, "/home/<USER>/workspace/migrations/meta/_journal.json": {"rootPath": "/home/<USER>/workspace", "relPath": "migrations/meta/_journal.json"}, "/home/<USER>/workspace/client/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": "client/index.html"}, "/home/<USER>/workspace/client/src/App.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/App.tsx"}, "/home/<USER>/workspace/client/src/index.css": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/index.css"}, "/home/<USER>/workspace/client/src/main.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/main.tsx"}, "/home/<USER>/workspace/client/src/utils/test-suites.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/utils/test-suites.ts"}, "/home/<USER>/workspace/client/src/utils/test-utils.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/utils/test-utils.ts"}, "/home/<USER>/workspace/package-lock.json": {"rootPath": "/home/<USER>/workspace", "relPath": "package-lock.json"}, "/home/<USER>/workspace/server/improved-voice-api.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/improved-voice-api.ts"}, "/home/<USER>/workspace/server/index.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/index.ts"}, "/home/<USER>/workspace/server/monitoring.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/monitoring.ts"}, "/home/<USER>/workspace/server/openai-realtime-proxy.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/openai-realtime-proxy.ts"}, "/home/<USER>/workspace/server/openai.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/openai.ts"}, "/home/<USER>/workspace/server/pg-direct.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/pg-direct.ts"}, "/home/<USER>/workspace/server/real-time-audio.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/real-time-audio.ts"}, "/home/<USER>/workspace/client/src/pages/AdminAITest.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/AdminAITest.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/task-storage/tasks/a8952510-18f3-457e-8b74-909845150301": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/task-storage/tasks/a8952510-18f3-457e-8b74-909845150301"}, "/home/<USER>/workspace/client/src/pages/AdminDashboard.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/AdminDashboard.tsx"}, "/home/<USER>/workspace/client/src/pages/Analytics.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/Analytics.tsx"}, "/home/<USER>/workspace/client/src/pages/ClientDetail.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/ClientDetail.tsx"}, "/home/<USER>/workspace/client/src/pages/Dashboard.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/Dashboard.tsx"}, "/home/<USER>/workspace/client/src/pages/LandingPage.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/LandingPage.tsx"}, "/home/<USER>/workspace/client/src/pages/Profile.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/Profile.tsx"}, "/home/<USER>/workspace/client/src/pages/SessionNotes.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/SessionNotes.tsx"}, "/home/<USER>/workspace/client/src/pages/Settings.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/Settings.tsx"}, "/home/<USER>/workspace/client/src/pages/VoiceTherapy.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/VoiceTherapy.tsx"}, "/home/<USER>/workspace/client/src/pages/auth-page.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/auth-page.tsx"}, "/home/<USER>/workspace/client/src/lib/protected-route.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/protected-route.tsx"}, "/home/<USER>/workspace/client/src/lib/utils.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/utils.ts"}, "/home/<USER>/workspace/client/src/hooks/use-auth.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/use-auth.tsx"}, "/home/<USER>/workspace/client/src/components/AdminVoiceTest.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/AdminVoiceTest.tsx"}, "/home/<USER>/workspace/client/src/components/ClientSession.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ClientSession.tsx"}, "/home/<USER>/workspace/client/src/components/ClientsList.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ClientsList.tsx"}, "/home/<USER>/workspace/client/src/components/ConfigManagement.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ConfigManagement.tsx"}, "/home/<USER>/workspace/client/src/components/DoctorDashboard.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/DoctorDashboard.tsx"}, "/home/<USER>/workspace/client/src/components/InsightsSidebar.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/InsightsSidebar.tsx"}, "/home/<USER>/workspace/client/src/components/Layout.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Layout.tsx"}, "/home/<USER>/workspace/client/src/components/MobileNav.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/MobileNav.tsx"}, "/home/<USER>/workspace/client/src/components/MobileOptimizations.txt": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/MobileOptimizations.txt"}, "/home/<USER>/workspace/client/src/components/NewNoteForm.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/NewNoteForm.tsx"}, "/home/<USER>/workspace/client/src/components/RealTimeVoiceChat.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RealTimeVoiceChat.tsx"}, "/home/<USER>/workspace/client/src/components/RealTimeVoiceTest.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RealTimeVoiceTest.tsx"}, "/home/<USER>/workspace/client/src/components/SessionNotesList.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/SessionNotesList.tsx"}, "/home/<USER>/workspace/client/src/components/Sidebar.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Sidebar.tsx"}, "/home/<USER>/workspace/client/src/components/SimpleVoiceChat.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/SimpleVoiceChat.tsx"}, "/home/<USER>/workspace/client/src/components/StatsCards.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/StatsCards.tsx"}, "/home/<USER>/workspace/client/src/components/TestSuiteRunner.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/TestSuiteRunner.tsx"}, "/home/<USER>/workspace/client/src/components/VoiceChat.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/VoiceChat.tsx"}, "/home/<USER>/workspace/client/src/components/ui/badge.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/badge.tsx"}, "/home/<USER>/workspace/client/src/components/ui/drawer.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/drawer.tsx"}, "/home/<USER>/workspace/client/src/components/ui/slider.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/slider.tsx"}, "/home/<USER>/workspace/client/src/assets/lama-logo.svg": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/assets/lama-logo.svg"}, "/home/<USER>/workspace/attached_assets/Pasted--LamaMind-com-is-a-compelling-and-evocative-domain-that-seamlessly-blends-the-ancient-wisdom-of-Tib-1745711967533.txt": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Pasted--<PERSON><PERSON><PERSON>-com-is-a-compelling-and-evocative-domain-that-seamlessly-blends-the-ancient-wisdom-of-Tib-1745711967533.txt"}, "/home/<USER>/workspace/attached_assets/Pasted--capturing-the-essence-of-ChatGPT-Advanced-Voice-Mode-s-Vale-voice-bright-inquisitive-and-engagi-1745711164512.txt": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Pasted--capturing-the-essence-of-ChatGPT-Advanced-Voice-Mode-s-Vale-voice-bright-inquisitive-and-engagi-1745711164512.txt"}, "/home/<USER>/workspace/attached_assets/Pasted-AI-Assisted-Therapy-Notes-Analyzer-Mental-Health-Professional-Tool-Problem-Opportunity-Therapists-1745707888003.txt": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Pasted-AI-Assisted-Therapy-Notes-Ana<PERSON><PERSON>-Mental-Health-Professional-Tool-Problem-Opportunity-Therapists-1745707888003.txt"}, "/home/<USER>/workspace/.upm/store.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".upm/store.json"}, "/home/<USER>/workspace/.local/state/replit/agent/.latest.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/state/replit/agent/.latest.json"}, "/home/<USER>/workspace/.local/state/replit/agent/rapid_build_started": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/state/replit/agent/rapid_build_started"}, "/home/<USER>/workspace/.local/state/replit/agent/rapid_build_success": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/state/replit/agent/rapid_build_success"}, "/home/<USER>/workspace/.config/.vscode-server/.cli.848b80aeb52026648a8ff9f7c45a9b0a80641e2e.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/.cli.848b80aeb52026648a8ff9f7c45a9b0a80641e2e.log"}, "/home/<USER>/workspace/.config/.vscode-server/.cli.f1a4fb101478ce6ec82fe9627c43efbf9e98c813.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/.cli.f1a4fb101478ce6ec82fe9627c43efbf9e98c813.log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/extensions.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/extensions.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/.vsixmanifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/.vsixmanifest"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/CHANGELOG.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/CHANGELOG.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/activitybar.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/activitybar.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/panel-icon-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/panel-icon-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/panel-icon-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/panel-icon-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/bg-next-edit-applied-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/bg-next-edit-applied-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/bg-next-edit-applied-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/bg-next-edit-applied-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/bg-next-edit-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/bg-next-edit-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/bg-next-edit-gray-hook.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/bg-next-edit-gray-hook.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/bg-next-edit-gray-line.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/bg-next-edit-gray-line.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/bg-next-edit-inactive-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/bg-next-edit-inactive-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/bg-next-edit-inactive-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/bg-next-edit-inactive-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/bg-next-edit-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/bg-next-edit-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/left-dark-disabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/left-dark-disabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/left-dark-enabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/left-dark-enabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/left-light-disabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/left-light-disabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/left-light-enabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/left-light-enabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-addition-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-addition-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-addition-inbetween-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-addition-inbetween-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-addition-inbetween-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-addition-inbetween-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-addition-inbetween-selected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-addition-inbetween-selected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-addition-inbetween-selected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-addition-inbetween-selected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-addition-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-addition-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-addition-selected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-addition-selected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-addition-selected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-addition-selected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-applied-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-applied-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-applied-inbetween-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-applied-inbetween-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-applied-inbetween-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-applied-inbetween-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-applied-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-applied-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-available-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-available-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-available-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-available-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-change-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-change-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-change-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-change-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-change-selected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-change-selected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-change-selected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-change-selected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-deletion-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-deletion-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-deletion-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-deletion-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-deletion-selected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-deletion-selected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-deletion-selected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-deletion-selected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-loading-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-loading-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-loading-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-loading-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-rejected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-rejected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-rejected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-rejected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-unavailable-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-unavailable-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-unavailable-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-unavailable-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-update-complete-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-update-complete-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-update-complete-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-update-complete-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-update-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-update-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-update-disabled-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-update-disabled-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-update-disabled-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-update-disabled-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-update-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-update-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-update-loading-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-update-loading-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-update-loading-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/nextedit-update-loading-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/right-dark-disabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/right-dark-disabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/right-dark-enabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/right-dark-enabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/right-light-disabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/right-light-disabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/right-light-enabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/next-edit/right-light-enabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/a.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/a.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/alt.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/alt.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/b.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/b.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/backspace.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/backspace.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/c.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/c.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/command.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/command.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/control.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/control.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/ctrl.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/ctrl.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/d.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/d.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/delete.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/delete.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/e.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/e.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/escape.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/escape.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/f.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/f.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/g.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/g.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/h.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/h.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/i.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/i.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/j.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/j.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/k.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/k.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/l.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/l.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/m.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/m.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/meta.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/meta.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/n.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/n.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/o.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/o.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/option.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/option.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/p.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/p.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/q.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/q.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/r.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/r.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/return.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/return.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/s.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/s.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/semicolon.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/semicolon.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/shift.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/shift.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/t.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/t.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/tab.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/tab.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/u.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/u.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/v.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/v.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/w.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/w.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/win.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/win.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/x.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/x.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/y.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/y.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/z.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/light/z.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/a.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/a.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/alt.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/alt.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/b.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/b.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/backspace.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/backspace.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/c.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/c.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/command.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/command.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/control.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/control.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/ctrl.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/ctrl.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/d.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/d.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/delete.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/delete.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/e.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/e.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/escape.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/escape.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/f.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/f.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/g.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/g.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/h.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/h.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/i.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/i.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/j.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/j.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/k.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/k.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/l.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/l.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/m.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/m.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/meta.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/meta.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/n.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/n.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/o.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/o.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/option.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/option.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/p.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/p.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/q.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/q.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/r.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/r.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/return.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/return.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/s.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/s.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/semicolon.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/semicolon.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/shift.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/shift.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/t.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/t.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/tab.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/tab.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/u.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/u.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/v.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/v.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/w.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/w.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/win.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/win.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/x.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/x.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/y.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/y.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/z.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/media/keyboard/dark/z.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/autofix.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/autofix.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/diff-view.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/diff-view.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/history.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/history.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/index.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/main-panel.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/main-panel.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/memories.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/memories.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/next-edit-suggestions.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/next-edit-suggestions.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/preference.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/preference.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/remote-agent-diff.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/remote-agent-diff.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/remote-agent-home.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/remote-agent-home.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/rules.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/rules.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/settings.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/settings.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/AugmentMessage-Bix3vTO2.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/AugmentMessage-Bix3vTO2.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/BaseButton-D8yhCvaJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/BaseButton-D8yhCvaJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/BaseButton-DvMdfQ3F.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/BaseButton-DvMdfQ3F.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/ButtonAugment-CNK8zC8i.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/ButtonAugment-CNK8zC8i.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/ButtonAugment-CRFFE3_i.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/ButtonAugment-CRFFE3_i.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/CardAugment-BAO5rOsN.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/CardAugment-BAO5rOsN.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/CardAugment-BAo8Ti0V.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/CardAugment-BAo8Ti0V.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/Content-D0WttAzY.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/Content-D0WttAzY.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/Content-xvE836E_.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/Content-xvE836E_.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/Drawer-DwFbLE28.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/Drawer-DwFbLE28.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/Drawer-rVeAyJPn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/Drawer-rVeAyJPn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/Filespan-DeFTcAEj.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/Filespan-DeFTcAEj.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/Filespan-tclW2Ian.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/Filespan-tclW2Ian.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/IconButtonAugment-BQL_8yIN.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/IconButtonAugment-BQL_8yIN.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/IconButtonAugment-BTu-iglL.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/IconButtonAugment-BTu-iglL.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/IconFilePath-3SJFMIIx.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/IconFilePath-3SJFMIIx.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/IconFilePath-CiKel2Kp.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/IconFilePath-CiKel2Kp.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/Keybindings-BFFBoxX3.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/Keybindings-BFFBoxX3.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/LanguageIcon-CA8dtZ_C.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/LanguageIcon-CA8dtZ_C.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/LanguageIcon-D78BqCXT.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/LanguageIcon-D78BqCXT.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/MarkdownEditor-DS0MQUBe.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/MarkdownEditor-DS0MQUBe.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/MaterialIcon-BO_oU5T3.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/MaterialIcon-BO_oU5T3.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/MaterialIcon-D8Nb6HkU.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/MaterialIcon-D8Nb6HkU.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/MessageList-C-ZrYdTG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/MessageList-C-ZrYdTG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/MessageList-jcql_8od.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/MessageList-jcql_8od.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/NextEditSuggestions-DfA5IcTZ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/NextEditSuggestions-DfA5IcTZ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/OpenFileButton-BO1gXf_-.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/OpenFileButton-BO1gXf_-.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/RemoteAgentRetry-CgKZWHFz.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/RemoteAgentRetry-CgKZWHFz.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/RemoteAgentSetup-B1m5qM5w.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/RemoteAgentSetup-B1m5qM5w.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/RulesDropdown-DpPttkHn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/RulesDropdown-DpPttkHn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/SpinnerAugment-DnPofOlT.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/SpinnerAugment-DnPofOlT.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/SpinnerAugment-JC8TPhVf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/SpinnerAugment-JC8TPhVf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/StatusIndicator-BiyeFzqm.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/StatusIndicator-BiyeFzqm.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/StatusIndicator-D-yOSWp9.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/StatusIndicator-D-yOSWp9.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/VirtualizedMessageList-DhWSVHYH.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/VirtualizedMessageList-DhWSVHYH.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/_basePickBy-ByUIyOvu.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/_basePickBy-ByUIyOvu.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/apex-DFVco9Dq.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/apex-DFVco9Dq.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/arc-DDQS7sFF.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/arc-DDQS7sFF.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/arrow-up-right-from-square-q_X4-2Am.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/arrow-up-right-from-square-q_X4-2Am.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/augment-logo-E1jEbeRV.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/augment-logo-E1jEbeRV.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/autofix-CX5hiNi4.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/autofix-CX5hiNi4.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/autofix-UGObdgvG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/autofix-UGObdgvG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/autofix-state-d-ymFdyn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/autofix-state-d-ymFdyn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/await_block-C0teov-5.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/await_block-C0teov-5.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/azcli-1IWB1ccx.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/azcli-1IWB1ccx.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/azcli-CBeeoD2V.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/azcli-CBeeoD2V.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/bat-DPkNLes8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/bat-DPkNLes8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/bicep-BZbtZWRn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/bicep-BZbtZWRn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/blockDiagram-ZHA2E4KO-B42N9sQ-.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/blockDiagram-ZHA2E4KO-B42N9sQ-.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/cameligo-CGrWLZr3.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/cameligo-CGrWLZr3.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/cameligo-hfF0gFWA.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/cameligo-hfF0gFWA.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/chunk-5HRBRIJM-CPol3-7a.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/chunk-5HRBRIJM-CPol3-7a.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/chunk-7U56Z5CX-CvYEYBIo.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/chunk-7U56Z5CX-CvYEYBIo.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/chunk-ASOPGD6M-B23XBs6b.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/chunk-ASOPGD6M-B23XBs6b.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/chunk-KFBOBJHC-eUOK71PW.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/chunk-KFBOBJHC-eUOK71PW.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/clojure-D9WOWImG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/clojure-D9WOWImG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/clone-Bevt1wGR.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/clone-Bevt1wGR.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/coffee-B7EJu28W.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/coffee-B7EJu28W.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/coffee-D3gVwdtb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/coffee-D3gVwdtb.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/cpp-B6k-yq-r.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/cpp-B6k-yq-r.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/cpp-DghbrAFl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/cpp-DghbrAFl.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/csharp-1bC6NAu3.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/csharp-1bC6NAu3.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/csharp-BoL64M5l.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/csharp-BoL64M5l.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/csp-ZI2qu8Le.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/csp-ZI2qu8Le.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/css-BkD51DMU.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/css-BkD51DMU.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/css-DQU6DXDx.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/css-DQU6DXDx.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/design-system-init-Bf1-mlh4.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/design-system-init-Bf1-mlh4.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/diagram-QW4FP2JN-D4E3DDnt.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/diagram-QW4FP2JN-D4E3DDnt.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/diff-utils-DB7Uu6wb.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/diff-utils-DB7Uu6wb.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/ecl-DrG4DZS2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/ecl-DrG4DZS2.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/elixir-nOQiPlLZ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/elixir-nOQiPlLZ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/exclamation-triangle-DfKf7sb_.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/exclamation-triangle-DfKf7sb_.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/file-paths-BcSg4gks.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/file-paths-BcSg4gks.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/file-reader-B7W_DzJn.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/file-reader-B7W_DzJn.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/TextAreaAugment-DxOmi7vy.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/TextAreaAugment-DxOmi7vy.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/VSCodeCodicon-DVaocTud.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/VSCodeCodicon-DVaocTud.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/VSCodeCodicon-zeLUoeQd.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/VSCodeCodicon-zeLUoeQd.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/_baseUniq-CCQ7Spk_.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/_baseUniq-CCQ7Spk_.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/abap-BrlRCFwh.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/abap-BrlRCFwh.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/abap-CRCWOmpq.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/abap-CRCWOmpq.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/apex-BE2Kqs_0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/apex-BE2Kqs_0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/architectureDiagram-UYN6MBPD-QswZRqS0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/architectureDiagram-UYN6MBPD-QswZRqS0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/bat-CtWuqYvB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/bat-CtWuqYvB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/bicep-C6yweCii.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/bicep-C6yweCii.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/c4Diagram-6F5ED5ID-BVV6e-uj.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/c4Diagram-6F5ED5ID-BVV6e-uj.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/channel-cfIDR5JC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/channel-cfIDR5JC.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/chat-flags-model-u-ZFJEJp.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/chat-flags-model-u-ZFJEJp.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/chat-types-NgqNgjwU.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/chat-types-NgqNgjwU.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/chevron-down-B-gSyyd4.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/chevron-down-B-gSyyd4.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/chunk-T2TOU4HS-sCAj2xSA.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/chunk-T2TOU4HS-sCAj2xSA.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/chunk-TMUBEWPD-DX6ogYvz.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/chunk-TMUBEWPD-DX6ogYvz.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/classDiagram-LNE6IOMH-BmTsqCrR.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/classDiagram-LNE6IOMH-BmTsqCrR.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/classDiagram-v2-MQ7JQ4JX-BmTsqCrR.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/classDiagram-v2-MQ7JQ4JX-BmTsqCrR.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/clojure-BhAVYYK7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/clojure-BhAVYYK7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/csp-C46ZqvIl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/csp-C46ZqvIl.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/cssMode-Cf0wo1J6.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/cssMode-Cf0wo1J6.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/cssMode-CnVX4sPg.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/cssMode-CnVX4sPg.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/cypher-D84EuPTj.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/cypher-D84EuPTj.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/cypher-DQ3GyGCv.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/cypher-DQ3GyGCv.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/cytoscape.esm-B0yNE0-9.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/cytoscape.esm-B0yNE0-9.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/dagre-4EVJKHTY-CK_G8ZIj.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/dagre-4EVJKHTY-CK_G8ZIj.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/dart-CQal6Qht.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/dart-CQal6Qht.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/dart-D8lhlL1r.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/dart-D8lhlL1r.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/design-system-init-CRmW_T8r.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/design-system-init-CRmW_T8r.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/design-system-init-CajyFoaO.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/design-system-init-CajyFoaO.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/diff-utils-DXaAmVnZ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/diff-utils-DXaAmVnZ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/dockerfile-CuMHdPl5.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/dockerfile-CuMHdPl5.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/ecl-BO6FnfXk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/ecl-BO6FnfXk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/elixir-BRjLKONM.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/elixir-BRjLKONM.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/ellipsis-ce3_p-Q7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/ellipsis-ce3_p-Q7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/erDiagram-6RL3IURR-CXiOESlE.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/erDiagram-6RL3IURR-CXiOESlE.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/expand-C7dSG_GJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/expand-C7dSG_GJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/flow9-Cac8vKd7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/flow9-Cac8vKd7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/flow9-DFOiqFq1.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/flow9-DFOiqFq1.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/freemarker2-DOiDZsWD.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/freemarker2-DOiDZsWD.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/go-CHYgS3dC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/go-CHYgS3dC.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/go-O9LJTZXk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/go-O9LJTZXk.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/schemas/jsconfig.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/schemas/jsconfig.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/schemas/tsconfig.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/schemas/tsconfig.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/resources/walkthroughs/create-a-js-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/resources/walkthroughs/create-a-js-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/resources/walkthroughs/debug-and-run.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/resources/walkthroughs/debug-and-run.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/resources/walkthroughs/install-node-js.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/resources/walkthroughs/install-node-js.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/resources/walkthroughs/learn-more.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/resources/walkthroughs/learn-more.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/tunnel-forwarding/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/tunnel-forwarding/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/tunnel-forwarding/.vscode/launch.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/tunnel-forwarding/.vscode/launch.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/htmlMode-DkXYjxNb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/htmlMode-DkXYjxNb.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/index-9HWdRmiB.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/index-9HWdRmiB.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/index-CW7fyhvB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/index-CW7fyhvB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/index-DiI90jLk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/index-DiI90jLk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/index-eY12-hdZ.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/index-eY12-hdZ.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/ini-BvajGCUy.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/ini-BvajGCUy.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/ini-COn9E3gi.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/ini-COn9E3gi.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/init-g68aIKmP.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/init-g68aIKmP.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/isObjectLike-BA2QYXi-.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/isObjectLike-BA2QYXi-.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/java-DBwYS35M.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/java-DBwYS35M.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/java-SYsfObOQ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/java-SYsfObOQ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/javascript-BrEubtUq.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/javascript-BrEubtUq.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/javascript-nb2UY8k5.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/javascript-nb2UY8k5.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/journeyDiagram-G5WM74LC-DKHd7ogN.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/journeyDiagram-G5WM74LC-DKHd7ogN.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/jsonMode-B6_b0_sN.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/jsonMode-B6_b0_sN.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/jsonMode-l8Gs1TaB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/jsonMode-l8Gs1TaB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/julia-ClS8lr_N.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/julia-ClS8lr_N.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/kanban-definition-QRCXZQQD-D6UkH-xw.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/kanban-definition-QRCXZQQD-D6UkH-xw.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/keypress-DD1aQVr0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/keypress-DD1aQVr0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/kotlin-CUUhw8ZM.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/kotlin-CUUhw8ZM.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/layer-group-Df_FYENN.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/layer-group-Df_FYENN.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/less-CW-yd8b8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/less-CW-yd8b8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/less-GGFNNJHn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/less-GGFNNJHn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/lexon-Canl7DCW.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/lexon-Canl7DCW.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/lexon-DwtVlf1I.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/lexon-DwtVlf1I.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/linear-kprw0b-H.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/linear-kprw0b-H.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/liquid-CMxlMAZJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/liquid-CMxlMAZJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/liquid-DOEm5dbE.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/liquid-DOEm5dbE.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/m3-B3V054Zg.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/m3-B3V054Zg.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/main-panel-DPyc9-tp.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/main-panel-DPyc9-tp.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/markdown-9NNSJ0ww.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/markdown-9NNSJ0ww.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/markdown-B811l8j2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/markdown-B811l8j2.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/mcp-logo-DUWKMrwv.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/mcp-logo-DUWKMrwv.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/mdx-BaH_mmJD.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/mdx-BaH_mmJD.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/mdx-CW_e0Iyn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/mdx-CW_e0Iyn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/memories-C6qJiBJA.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/memories-C6qJiBJA.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/memories-CMFr_HGh.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/memories-CMFr_HGh.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/Keybindings-lGeuBRaW.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/Keybindings-lGeuBRaW.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/MarkdownEditor-zNvUkrOp.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/MarkdownEditor-zNvUkrOp.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/NextEditSuggestions-C1kwmzU5.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/NextEditSuggestions-C1kwmzU5.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/OpenFileButton-CELS5VUG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/OpenFileButton-CELS5VUG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/RemoteAgentRetry-D8aC0VhB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/RemoteAgentRetry-D8aC0VhB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/TextTooltipAugment-BIMZ5dVo.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/TextTooltipAugment-BIMZ5dVo.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/TextTooltipAugment-BlDY2tAQ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/TextTooltipAugment-BlDY2tAQ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/VirtualizedMessageList-C7ukA2AO.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/VirtualizedMessageList-C7ukA2AO.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/diff-view-DcQh0Eu3.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/diff-view-DcQh0Eu3.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/diff-view-DwmCBV60.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/diff-view-DwmCBV60.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/dockerfile-DLk6rpji.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/dockerfile-DLk6rpji.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/file-reader-CXfsL4m-.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/file-reader-CXfsL4m-.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/flowDiagram-7ASYPVHJ-BWH3_aYT.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/flowDiagram-7ASYPVHJ-BWH3_aYT.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/folder-DRaPdzNV.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/folder-DRaPdzNV.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/folder-opened-bSDyFrZo.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/folder-opened-bSDyFrZo.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/freemarker2-DoNuTueB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/freemarker2-DoNuTueB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/fsharp-BpBzFqoi.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/fsharp-BpBzFqoi.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/fsharp-fd1GTHhf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/fsharp-fd1GTHhf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/ganttDiagram-NTVNEXSI-DvgVupBu.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/ganttDiagram-NTVNEXSI-DvgVupBu.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/history-BExMV7aR.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/history-BExMV7aR.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/history-DLzhBtGZ.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/history-DLzhBtGZ.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/html-CHnKu8C4.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/html-CHnKu8C4.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/htmlMode-CIRhgpF_.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/htmlMode-CIRhgpF_.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/server-cli.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/server-cli.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/CodeTabExpansion.psm1": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/CodeTabExpansion.psm1"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/GitTabExpansion.psm1": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/GitTabExpansion.psm1"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-env.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-env.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-login.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-login.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-profile.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-profile.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-rc.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-rc.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.ps1": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.ps1"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/fish_xdg_data/fish/vendor_conf.d/shellIntegration.fish": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/fish_xdg_data/fish/vendor_conf.d/shellIntegration.fish"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/readme.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/readme.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/bootloader.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/bootloader.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/diagnosticTool.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/diagnosticTool.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/diagnosticTool.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/diagnosticTool.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/hash.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/hash.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/renameWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/renameWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/vendor/acorn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/vendor/acorn.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/targets/node/terminateProcess.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/targets/node/terminateProcess.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/logo.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/logo.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/configure.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/configure.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/connect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/connect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/disconnect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/disconnect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/node.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/node.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/open-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/open-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/page.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/page.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/pause.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/pause.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/restart.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/restart.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/resume.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/resume.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/service-worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/service-worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/stop.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/stop.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/open-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/open-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/page.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/page.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/pause.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/pause.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/restart.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/restart.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/resume.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/resume.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/service-worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/service-worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/stop-profiling.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/stop-profiling.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/stop.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/stop.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/microsoft-authentication/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/microsoft-authentication/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/microsoft-authentication/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/microsoft-authentication/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/microsoft-authentication/media/auth.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/microsoft-authentication/media/auth.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/highlight.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/highlight.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/markdown.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/markdown.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/pre.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/pre.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/preview-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/preview-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/preview-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/preview-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/json-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/json-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/json-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/json-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/json-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/json-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/jake/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/jake/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/jake/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/jake/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/jake/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/jake/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ipynb/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ipynb/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ipynb/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ipynb/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ipynb/notebook-out/cellAttachmentRenderer.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ipynb/notebook-out/cellAttachmentRenderer.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/server/lib/jquery.d.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/server/lib/jquery.d.ts"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/gulp/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/gulp/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/gulp/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/gulp/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/grunt/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/grunt/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/grunt/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/grunt/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/grunt/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/grunt/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github-authentication/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github-authentication/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github-authentication/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github-authentication/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github-authentication/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github-authentication/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github-authentication/media/auth.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github-authentication/media/auth.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github-authentication/media/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github-authentication/media/index.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/index-BFtESN_v.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/index-BFtESN_v.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/index-McRKs1sU.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/index-McRKs1sU.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/index-yERhhNs7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/index-yERhhNs7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/infoDiagram-A4XQUW5V-CR6syjxN.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/infoDiagram-A4XQUW5V-CR6syjxN.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/julia-DQXNmw_w.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/julia-DQXNmw_w.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/katex-BAVf198l.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/katex-BAVf198l.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/kotlin-qQ0MG-9I.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/kotlin-qQ0MG-9I.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/layer-group-DyG7sLW7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/layer-group-DyG7sLW7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/layout-CoKHxiGD.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/layout-CoKHxiGD.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/lodash-C-61Uc4F.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/lodash-C-61Uc4F.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/lua-BdjVVLHC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/lua-BdjVVLHC.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/lua-D28Ae8-K.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/lua-D28Ae8-K.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/m3-Bu4mmWhs.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/m3-Bu4mmWhs.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/magnifying-glass-D5YyJVGd.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/magnifying-glass-D5YyJVGd.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/mindmap-definition-GWI6TPTV-BY_znqSx.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/mindmap-definition-GWI6TPTV-BY_znqSx.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/mips-CdjsipkG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/mips-CdjsipkG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/mips-Cu7FWeYr.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/mips-Cu7FWeYr.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/msdax-CYqgjx_P.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/msdax-CYqgjx_P.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/msdax-DBX3bZkL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/msdax-DBX3bZkL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/mysql-BHd6q0vd.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/mysql-BHd6q0vd.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/mysql-CMGNIvT0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/mysql-CMGNIvT0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/next-edit-suggestions-CKieFv1T.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/next-edit-suggestions-CKieFv1T.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/next-edit-suggestions-UoxOVKUP.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/next-edit-suggestions-UoxOVKUP.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/next-edit-suggestions-cyZwwI9B.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/next-edit-suggestions-cyZwwI9B.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/next-edit-types-904A5ehg.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/next-edit-types-904A5ehg.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/objective-c-BdAIHrxl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/objective-c-BdAIHrxl.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/objective-c-DCIC4Ga8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/objective-c-DCIC4Ga8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/open-in-new-window-C60603LX.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/open-in-new-window-C60603LX.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/ordinal-_rw2EY4v.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/ordinal-_rw2EY4v.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pascal-BhNW15KB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pascal-BhNW15KB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pascal-DVjYFmSU.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pascal-DVjYFmSU.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pascaligo-5jv8CcQD.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pascaligo-5jv8CcQD.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pascaligo-LOm9cWIk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pascaligo-LOm9cWIk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pen-to-square-3TLxExQu.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pen-to-square-3TLxExQu.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pen-to-square-Dvw-pMXw.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pen-to-square-Dvw-pMXw.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/perl-DlYyT36c.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/perl-DlYyT36c.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/perl-UpK8AUhB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/perl-UpK8AUhB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pgsql-Dy0bjov7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pgsql-Dy0bjov7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pgsql-cWj3SLw2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pgsql-cWj3SLw2.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/php-120yhfDK.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/php-120yhfDK.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/php-C92L-r_Y.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/php-C92L-r_Y.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pieDiagram-YF2LJOPJ-BnT3GqSm.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pieDiagram-YF2LJOPJ-BnT3GqSm.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pla-B-trYkKT.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pla-B-trYkKT.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pla-CjnFlu4u.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pla-CjnFlu4u.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/postiats-CQpG440k.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/postiats-CQpG440k.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/postiats-ToQhlN1R.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/postiats-ToQhlN1R.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/powerquery-BLkMU_zt.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/powerquery-BLkMU_zt.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/powerquery-DdJtto1Z.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/powerquery-DdJtto1Z.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/powershell-Bu_VLpJB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/powershell-Bu_VLpJB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/powershell-Cz-ePiwW.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/powershell-Cz-ePiwW.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/preference-Dn6mpF6J.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/preference-Dn6mpF6J.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/preference-o4_MmuJb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/preference-o4_MmuJb.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/preload-helper-Dv6uf1Os.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/preload-helper-Dv6uf1Os.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/protobuf-BQ74DTcm.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/protobuf-BQ74DTcm.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/resize-observer-DdAtcrRr.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/resize-observer-DdAtcrRr.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/RemoteAgentSetup-D3BVM7L0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/RemoteAgentSetup-D3BVM7L0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/TextAreaAugment-J75lFxU7.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/TextAreaAugment-J75lFxU7.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/folder-TcwSfzDI.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/folder-TcwSfzDI.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/html-gnlaprsJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/html-gnlaprsJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/stateDiagram-v2-4JROLMXI-DMnv92v7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/stateDiagram-v2-4JROLMXI-DMnv92v7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/twig-BfRIq3la.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/twig-BfRIq3la.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/twig-h6VuAx0U.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/twig-h6VuAx0U.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/typespec-5IKh-a8s.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/typespec-5IKh-a8s.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/wgsl-DCafy-vX.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/wgsl-DCafy-vX.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250605T012718/exthost1/vscode.github/GitHub.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250605T012718/exthost1/vscode.github/GitHub.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/Augment-Memories": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/Augment-Memories"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/files/node/watcher/watcherMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/files/node/watcher/watcherMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/base/node/cpuUsage.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/base/node/cpuUsage.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/base/node/ps.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/base/node/ps.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/media/codicon.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/media/codicon.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/media/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/media/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/media/main.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/media/main.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/media/preview-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/media/preview-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/media/preview-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/media/preview-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/search-result/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/search-result/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/search-result/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/search-result/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/search-result/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/search-result/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/search-result/syntaxes/searchResult.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/search-result/syntaxes/searchResult.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/references-view/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/references-view/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/references-view/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/references-view/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/references-view/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/references-view/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/php-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/php-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/php-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/php-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/php-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/php-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/npm/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/npm/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/npm/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/npm/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/npm/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/npm/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/npm/images/code.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/npm/images/code.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/ci.yml": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/ci.yml"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/tsconfig.browser.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/tsconfig.browser.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/425.heapsnapshotWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/425.heapsnapshotWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/848.extension.web.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/848.extension.web.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/cpu-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/cpu-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.web.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.web.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/heap-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/heap-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshot-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshot-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshotWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshotWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/SECURITY.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/SECURITY.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/ci.yml": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/ci.yml"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/eslint.config.mjs": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/eslint.config.mjs"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/out/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/out/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/base/node/cpuUsage.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/base/node/cpuUsage.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/base/node/ps.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/base/node/ps.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/schemas/jsconfig.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/schemas/jsconfig.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/schemas/tsconfig.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/schemas/tsconfig.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/create-a-js-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/create-a-js-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/learn-more.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/learn-more.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/tunnel-forwarding/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/tunnel-forwarding/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/tunnel-forwarding/.vscode/launch.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/tunnel-forwarding/.vscode/launch.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/.gitignore": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/.gitignore"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/codicon.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/codicon.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/main.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/main.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/syntaxes/searchResult.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/syntaxes/searchResult.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/references-view/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/references-view/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/references-view/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/references-view/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/php-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/php-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/images/code.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/images/code.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/ci.yml": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/ci.yml"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/tsconfig.browser.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/tsconfig.browser.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/848.extension.web.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/848.extension.web.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/xml-_u1XISHN.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/xml-_u1XISHN.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/handlebars-CNQw3EXp.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/handlebars-CNQw3EXp.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/qsharp-q7JyzKFN.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/qsharp-q7JyzKFN.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/merge-conflict/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/merge-conflict/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/imagePreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/imagePreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/types-Cgd-nZOV.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/types-Cgd-nZOV.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/emojis.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/emojis.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/restart.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/restart.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/vendor/acorn-loose.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/vendor/acorn-loose.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-conflict.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-conflict.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/media/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/media/index.html"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/audioPreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/audioPreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/vendor/acorn-loose.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/vendor/acorn-loose.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/resume.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/resume.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/solidity-C4mwTkrB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/solidity-C4mwTkrB.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/resume.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/resume.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/scheme-Ecrf_Zyn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/scheme-Ecrf_Zyn.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/ci.yml": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/ci.yml"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/connect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/connect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-deleted.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-deleted.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/tcl-Bl2hYPt-.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/tcl-Bl2hYPt-.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/types-BSMhNRWH.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/types-BSMhNRWH.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/install-node-js.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/install-node-js.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/syntaxes/md-math-block.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/syntaxes/md-math-block.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/redshift-CBifECDb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/redshift-CBifECDb.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-type-changed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-type-changed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/logo.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/logo.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/pause.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/pause.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/grunt/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/grunt/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/emojis.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/emojis.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/sql-BdTr02Mf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/sql-BdTr02Mf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/protobuf-CZXszgil.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/protobuf-CZXszgil.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/notebook-out/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/notebook-out/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/preview-styles/index.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/preview-styles/index.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/scheme-Dhb-2j9p.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/scheme-Dhb-2j9p.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-rc.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-rc.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/sql-BV61QDTH.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/sql-BV61QDTH.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/gulp/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/gulp/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/php-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/php-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/shell-CNhb_Zkf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/shell-CNhb_Zkf.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/stop.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/stop.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/globals-D0QH3NT1.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/globals-D0QH3NT1.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/swift-DqwpnxQL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/swift-DqwpnxQL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/tsMode-DM5zRHFn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/tsMode-DM5zRHFn.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/python-CZ67Wo4I.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/python-CZ67Wo4I.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/sb-Ddgo-Lel.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/sb-Ddgo-Lel.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/audioPreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/audioPreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/quadrantDiagram-OS5C2QUG-BvCri0JZ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/quadrantDiagram-OS5C2QUG-BvCri0JZ.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/restart.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/restart.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/microsoft-authentication/media/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/microsoft-authentication/media/index.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/r-DShZCeRJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/r-DShZCeRJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/razor-BxlDHIuM.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/razor-BxlDHIuM.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250605T012718/remoteTelemetry.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250605T012718/remoteTelemetry.log"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/configure.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/configure.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/preview-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/preview-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/redshift-6xAzNskS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/redshift-6xAzNskS.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/disconnect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/disconnect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/languages/git-rebase.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/languages/git-rebase.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ipynb/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ipynb/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/hcl-CVzGlmMO.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/hcl-CVzGlmMO.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/graph-DrJmcQuC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/graph-DrJmcQuC.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/languages/ignore.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/languages/ignore.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/watchdog.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/watchdog.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-conflict.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-conflict.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/scss-DuQSCaUL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/scss-DuQSCaUL.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/configure.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/configure.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/targets/node/terminateProcess.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/targets/node/terminateProcess.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/vendor/acorn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/vendor/acorn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/typespec-DKGjpBXL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/typespec-DKGjpBXL.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/connect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/connect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/preview-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/preview-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/syntaxes/md-math.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/syntaxes/md-math.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/graphql-LQdxqEYJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/graphql-LQdxqEYJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/stop-profiling.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/stop-profiling.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/st-BZ7aq21L.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/st-BZ7aq21L.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/hash.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/hash.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/diagnosticTool.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/diagnosticTool.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/scala-Bqvq8jcR.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/scala-Bqvq8jcR.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-ignored.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-ignored.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/qsharp-YKUDF0Oj.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/qsharp-YKUDF0Oj.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/scala-Bzjcj0lf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/scala-Bzjcj0lf.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-untracked.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-untracked.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.web.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.web.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-modified.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-modified.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/425.heapsnapshotWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/425.heapsnapshotWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/node.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/node.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-added.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-added.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pug-kFxLfcjb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pug-kFxLfcjb.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/r-BIFz-_sK.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/r-BIFz-_sK.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/timeline-definition-U7ZMHBDA-B2HloZLT.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/timeline-definition-U7ZMHBDA-B2HloZLT.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/systemverilog-DgMryOEJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/systemverilog-DgMryOEJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshot-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshot-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/service-worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/service-worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/bootloader.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/bootloader.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/disconnect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/disconnect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/audioPreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/audioPreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/tsMode-CQlxoi98.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/tsMode-CQlxoi98.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250605T012718/remoteagent.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250605T012718/remoteagent.log"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/syntaxes/md-math-fence.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/syntaxes/md-math-fence.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/emmet/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/emmet/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/out/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/out/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/node.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/node.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/types-B5Ac2hek.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/types-B5Ac2hek.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/scss-CTwUZ5N7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/scss-CTwUZ5N7.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/readme.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/readme.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/configuration-editing/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/configuration-editing/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/videoPreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/videoPreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-deleted.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-deleted.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/typescript-Dst5H8CP.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/typescript-Dst5H8CP.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/connect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/connect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/configure.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/configure.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/python-DW6CoqQ2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/python-DW6CoqQ2.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/razor-CgmX9w71.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/razor-CgmX9w71.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/swift-D7IUmUK8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/swift-D7IUmUK8.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/lib/jquery.d.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/lib/jquery.d.ts"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/loading-hc.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/loading-hc.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-added.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-added.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/hcl-DxDQ3s82.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/hcl-DxDQ3s82.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pug-8ix3pnNZ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/pug-8ix3pnNZ.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/typescript-DTP-A_Zf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/typescript-DTP-A_Zf.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/diagnosticTool.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/diagnosticTool.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/notebook-out/katex.min.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/notebook-out/katex.min.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/wgsl-Du36xR5C.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/wgsl-Du36xR5C.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/some-markdown.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/some-markdown.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/videoPreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/videoPreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/extension-editing/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/extension-editing/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/handlebars-SegiTBbt.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/handlebars-SegiTBbt.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/tcl-PloMZuKG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/tcl-PloMZuKG.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/open-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/open-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/sparql-CouE6pZG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/sparql-CouE6pZG.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/merge-conflict/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/merge-conflict/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/redis-CHOsPHWR.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/redis-CHOsPHWR.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/telemetry.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/telemetry.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/stop.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/stop.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-type-changed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-type-changed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/yaml-CRGTkk5g.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/yaml-CRGTkk5g.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/page.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/page.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshotWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshotWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/disconnect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/disconnect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-renamed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-renamed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/cpu-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/cpu-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/vb-CS586MRk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/vb-CS586MRk.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-copied.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-copied.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/service-worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/service-worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/terminal-BBUsFUTj.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/terminal-BBUsFUTj.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/xml-C0Ah6gFk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/xml-C0Ah6gFk.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/loading-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/loading-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/syntaxes/md-math-inline.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/syntaxes/md-math-inline.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/node.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/node.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/stateDiagram-MAYHULR4-B09gtFBb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/stateDiagram-MAYHULR4-B09gtFBb.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/heap-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/heap-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/loading.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/loading.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/sequenceDiagram-G6AWOVSC-Ctst-i89.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/sequenceDiagram-G6AWOVSC-Ctst-i89.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/github-Du8Ax-RE.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/github-Du8Ax-RE.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/redis-DJMpkPfA.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/redis-DJMpkPfA.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/emmet/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/emmet/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/SECURITY.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/SECURITY.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/systemverilog-CeZ7LPTL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/systemverilog-CeZ7LPTL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/gitGraph-YCYPL57B-C02lEo3t.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/gitGraph-YCYPL57B-C02lEo3t.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/vb-BwAE3J76.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/vb-BwAE3J76.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/markdown.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/markdown.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/eslint.config.mjs": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/eslint.config.mjs"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/gitGraphDiagram-NRZ2UAAF-B8Y9qNF2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/gitGraphDiagram-NRZ2UAAF-B8Y9qNF2.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/merge-conflict/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/merge-conflict/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/yaml-Bo9nk_ct.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/yaml-Bo9nk_ct.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/st-C7iG7M4S.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/st-C7iG7M4S.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/graphql-csByOneL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/graphql-csByOneL.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/renameWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/renameWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250605T012718/exthost1/remoteExtHostTelemetry.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250605T012718/exthost1/remoteExtHostTelemetry.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250605T012639/ptyhost.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250605T012639/ptyhost.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250605T012639/remoteagent.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250605T012639/remoteagent.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250605T012639/exthost1/remoteexthost.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250605T012639/exthost1/remoteexthost.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250605T012639/exthost1/vscode.json-language-features/JSON Language Server.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250605T012639/exthost1/vscode.json-language-features/JSON Language Server.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250605T012639/exthost1/vscode.github/GitHub.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250605T012639/exthost1/vscode.github/GitHub.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250605T012639/exthost1/vscode.git/Git.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250605T012639/exthost1/vscode.git/Git.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/task-storage/tasks/7010bd30-91d6-49a5-ada6-bb3fbbe4be9e": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/task-storage/tasks/7010bd30-91d6-49a5-ada6-bb3fbbe4be9e"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/task-storage/tasks/c43012f7-2cc1-494d-ab34-d6d0f88cc413": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/task-storage/tasks/c43012f7-2cc1-494d-ab34-d6d0f88cc413"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/task-storage/tasks/d4df710d-f181-4b13-9885-ffb8da904309": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/task-storage/tasks/d4df710d-f181-4b13-9885-ffb8da904309"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/task-storage/manifest/manifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/task-storage/manifest/manifest"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/agent-edit-shard-storage-manifest.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/agent-edit-shard-storage-manifest.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/lru.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/lru.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/log.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/log.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/pid.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/pid.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/LICENSE": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/LICENSE"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/product.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/product.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/bootstrap-fork.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/bootstrap-fork.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/debug-and-run.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/debug-and-run.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/watchdog.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/watchdog.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/open-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/open-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/page.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/page.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/pause.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/pause.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/imagePreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/imagePreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/notebook-out/katex.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/notebook-out/katex.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/syntaxes/git-commit.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/syntaxes/git-commit.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/syntaxes/git-rebase.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/syntaxes/git-rebase.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/syntaxes/ignore.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/syntaxes/ignore.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/languages/git-commit.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/languages/git-commit.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-copied.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-copied.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-deleted.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-deleted.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-ignored.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-ignored.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-modified.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-modified.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-renamed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-renamed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-untracked.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-untracked.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/extension-editing/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/extension-editing/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/emmet/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/emmet/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/debug-server-ready/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/debug-server-ready/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/debug-server-ready/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/debug-server-ready/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/debug-auto-launch/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/debug-auto-launch/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/debug-auto-launch/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/debug-auto-launch/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/debug-auto-launch/.vscode/launch.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/debug-auto-launch/.vscode/launch.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/configuration-editing/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/configuration-editing/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/configuration-editing/schemas/attachContainer.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/configuration-editing/schemas/attachContainer.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/bin/code-server": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/bin/code-server"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/bin/remote-cli/code": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/bin/remote-cli/code"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/bin/helpers/browser.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/bin/helpers/browser.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/bin/helpers/check-requirements.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/bin/helpers/check-requirements.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/log.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/log.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/pid.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/pid.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/LICENSE": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/LICENSE"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/product.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/product.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/bootstrap-fork.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/bootstrap-fork.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-env.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-env.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-login.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-login.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-profile.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-profile.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.fish": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.fish"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.ps1": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.ps1"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/remote-agent-diff-CHAwCLsJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/remote-agent-diff-CHAwCLsJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/remote-agent-diff-CekNHzOv.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/remote-agent-diff-CekNHzOv.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/remote-agent-home-BdKt9UZP.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/remote-agent-home-BdKt9UZP.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/remote-agent-home-DXGXtJVn.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/remote-agent-home-DXGXtJVn.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/requirementDiagram-MIRIMTAZ-Xqk9wj-p.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/requirementDiagram-MIRIMTAZ-Xqk9wj-p.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/restructuredtext-CQoPj0uC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/restructuredtext-CQoPj0uC.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/restructuredtext-CghPJEOS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/restructuredtext-CghPJEOS.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/ruby-1H8dtvFl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/ruby-1H8dtvFl.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/settings-5JV9aqQp.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/settings-5JV9aqQp.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/settings-BUgdOl3i.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/settings-BUgdOl3i.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/shell-CsDZo4DB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/shell-CsDZo4DB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/solidity-CME5AdoB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/solidity-CME5AdoB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/sophia-RYC1BQQz.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/sophia-RYC1BQQz.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/sophia-dWwzI90F.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/sophia-dWwzI90F.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/sparql-KEyrF7De.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/sparql-KEyrF7De.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/test_service_pb-DM0n7l7E.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/test_service_pb-DM0n7l7E.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/toggleHighContrast-D4zjdeIP.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/toggleHighContrast-D4zjdeIP.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/xychartDiagram-6QU3TZC5-C_gmEu-E.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/xychartDiagram-6QU3TZC5-C_gmEu-E.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/machineid": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/machineid"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250605T012718/ptyhost.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250605T012718/ptyhost.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250605T012718/exthost1/remoteexthost.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250605T012718/exthost1/remoteexthost.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250605T012718/exthost1/vscode.json-language-features/JSON Language Server.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250605T012718/exthost1/vscode.json-language-features/JSON Language Server.log"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/files/node/watcher/watcherMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/files/node/watcher/watcherMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/media/auth.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/media/auth.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/audioPreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/audioPreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/loading-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/loading-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/loading-hc.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/loading-hc.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/loading.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/loading.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/videoPreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/videoPreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/videoPreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/videoPreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math-block.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math-block.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math-fence.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math-fence.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math-inline.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math-inline.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/preview-styles/index.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/preview-styles/index.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/notebook-out/katex.min.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/notebook-out/katex.min.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/highlight.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/highlight.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/pre.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/pre.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/preview-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/preview-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/jake/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/jake/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/gulp/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/gulp/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/grunt/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/grunt/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/media/auth.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/media/auth.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/media/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/media/index.html"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/markdown.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/markdown.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/some-markdown.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/some-markdown.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/syntaxes/git-commit.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/syntaxes/git-commit.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/syntaxes/git-rebase.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/syntaxes/git-rebase.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/syntaxes/ignore.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/syntaxes/ignore.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/languages/git-commit.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/languages/git-commit.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/languages/git-rebase.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/languages/git-rebase.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/languages/ignore.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/languages/ignore.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-added.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-added.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-conflict.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-conflict.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-copied.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-copied.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-cli.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-cli.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/ruby-CYWGW-b1.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/ruby-CYWGW-b1.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/rules-DecO7AHT.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/rules-DecO7AHT.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/rules-aCkY5vTw.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/rules-aCkY5vTw.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/rust-APfvjYow.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/rust-APfvjYow.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/rust-DMDD0SHb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/rust-DMDD0SHb.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/sankeyDiagram-Y46BX6SQ-CTTpeRW4.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/sankeyDiagram-Y46BX6SQ-CTTpeRW4.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/sb-BYAiYHFx.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.1/common-webviews/assets/sb-BYAiYHFx.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/imagePreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/imagePreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/imagePreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/imagePreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/notebook-out/katex.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/notebook-out/katex.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/notebook-out/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/notebook-out/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/markdown.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/markdown.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/preview-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/preview-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/jake/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/jake/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/notebook-out/cellAttachmentRenderer.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/notebook-out/cellAttachmentRenderer.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-ignored.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-ignored.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-modified.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-modified.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-renamed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-renamed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-type-changed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-type-changed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-untracked.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-untracked.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-added.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-added.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-conflict.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-conflict.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-copied.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-copied.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-deleted.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-deleted.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-ignored.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-ignored.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-modified.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-modified.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-renamed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-renamed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-type-changed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-type-changed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-untracked.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-untracked.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/extension-editing/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/extension-editing/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/emmet/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/emmet/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/emmet/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/emmet/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-server-ready/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-server-ready/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-auto-launch/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-auto-launch/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-auto-launch/.vscode/launch.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-auto-launch/.vscode/launch.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/configuration-editing/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/configuration-editing/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/configuration-editing/schemas/attachContainer.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/configuration-editing/schemas/attachContainer.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/code-server": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/code-server"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/remote-cli/code": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/remote-cli/code"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/helpers/browser.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/helpers/browser.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/helpers/check-requirements.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/helpers/check-requirements.sh"}, "/home/<USER>/workspace/.cache/typescript/5.7/package-lock.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/typescript/5.7/package-lock.json"}, "/home/<USER>/workspace/.cache/replit/modules.stamp": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules.stamp"}, "/home/<USER>/workspace/.cache/replit/toolchain.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/toolchain.json"}, "/home/<USER>/workspace/.cache/replit/nix/dotreplitenv.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/nix/dotreplitenv.json"}, "/home/<USER>/workspace/.cache/replit/modules/nodejs-20.res": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/nodejs-20.res"}, "/home/<USER>/workspace/.cache/replit/modules/postgresql-16.res": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/postgresql-16.res"}, "/home/<USER>/workspace/.cache/replit/modules/replit.res": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/replit.res"}, "/home/<USER>/workspace/.cache/replit/modules/web.res": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/web.res"}, "/home/<USER>/workspace/.cache/replit/env/latest": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/env/latest"}, "/home/<USER>/workspace/.cache/replit/env/latest.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/env/latest.json"}, "/home/<USER>/workspace/.cache/Microsoft/DeveloperTools/deviceid": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/Microsoft/DeveloperTools/deviceid"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250605T012718/exthost1/vscode.typescript-language-features/TypeScript.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250605T012718/exthost1/vscode.typescript-language-features/TypeScript.log"}}