#!/usr/bin/env node

import { WebSocket } from 'ws';

const WEBSOCKET_URL = 'ws://localhost:5000/ws';

console.log('🔍 Debugging Real-time Conversation Flow...');
console.log('📡 Connecting to:', WEBSOCKET_URL);

const ws = new WebSocket(WEBSOCKET_URL);

let messageCount = 0;
const receivedMessages = [];
const sentMessages = [];

ws.on('open', () => {
  console.log('✅ WebSocket connected');
  
  // Send start message to initialize Realtime API session
  const startMessage = {
    type: "start",
    userId: "debug-user",
    clientId: "debug-client",
    useRealtimeAPI: true,
    mode: "realtime",
    behavior: {
      model: "gpt-4o-realtime-preview",
      temperature: 0.7,
      voice: {
        voice: "shimmer",
        speed: 1.0
      }
    },
    instructions: "You are <PERSON>, an empathetic AI therapeutic assistant. Respond thoughtfully and supportively to help users process their emotions and thoughts. Keep responses concise and natural for voice conversation."
  };
  
  console.log('📤 Sending start message...');
  ws.send(JSON.stringify(startMessage));
  sentMessages.push('start');
});

ws.on('message', (data) => {
  messageCount++;
  try {
    const message = JSON.parse(data.toString());
    console.log(`📥 Message ${messageCount}: ${message.type}`);
    receivedMessages.push(message.type);
    
    // Handle specific message types
    switch (message.type) {
      case 'ready':
        console.log('✅ Session ready! Testing audio input simulation...');
        
        // Simulate sending audio data after a short delay
        setTimeout(() => {
          console.log('🎤 Simulating audio input...');
          
          // Generate some fake PCM16 audio data (silence with a bit of noise)
          const sampleRate = 24000;
          const durationMs = 1000; // 1 second
          const samples = Math.floor(sampleRate * durationMs / 1000);
          const audioBuffer = new Int16Array(samples);
          
          // Add some random noise to simulate speech
          for (let i = 0; i < samples; i++) {
            audioBuffer[i] = Math.floor((Math.random() - 0.5) * 1000); // Low amplitude noise
          }
          
          // Convert to base64
          const buffer = Buffer.from(audioBuffer.buffer);
          const base64Audio = buffer.toString('base64');
          
          console.log(`📤 Sending audio chunk (${base64Audio.length} chars)...`);
          
          // Send audio data
          ws.send(JSON.stringify({
            type: 'input_audio_buffer.append',
            audio: base64Audio
          }));
          sentMessages.push('input_audio_buffer.append');
          
          // Commit the audio after a short delay
          setTimeout(() => {
            console.log('📤 Committing audio buffer...');
            ws.send(JSON.stringify({
              type: 'input_audio_buffer.commit'
            }));
            sentMessages.push('input_audio_buffer.commit');
          }, 100);
          
        }, 1000);
        break;
        
      case 'conversation.item.input_audio_transcription.completed':
        console.log(`🎯 User speech transcribed: "${message.transcript}"`);
        break;
        
      case 'response.audio.delta':
        console.log('🔊 AI audio response chunk received');
        break;
        
      case 'response.audio_transcript.done':
        console.log(`🤖 AI response: "${message.transcript}"`);
        break;
        
      case 'response.done':
        console.log('✅ Response completed');
        
        // End the test after receiving a complete response
        setTimeout(() => {
          console.log('📤 Ending conversation...');
          ws.send(JSON.stringify({
            type: 'conversationEnded'
          }));
          sentMessages.push('conversationEnded');
        }, 1000);
        break;
        
      case 'end_ack':
        console.log('✅ Conversation ended');
        
        // Close connection and show summary
        setTimeout(() => {
          console.log('\n=== DEBUG SUMMARY ===');
          console.log('Sent messages:', sentMessages);
          console.log('Received messages:', receivedMessages);
          console.log('✅ Debug test completed');
          ws.close();
        }, 500);
        break;
        
      case 'error':
        console.error('❌ Error received:', message.message);
        break;
        
      default:
        // Just log other message types
        break;
    }
  } catch (error) {
    console.error('❌ Error parsing message:', error);
  }
});

ws.on('close', (code, reason) => {
  console.log(`🔌 WebSocket closed: ${code} ${reason.toString()}`);
});

ws.on('error', (error) => {
  console.error('❌ WebSocket error:', error);
});

// Timeout to prevent hanging
setTimeout(() => {
  console.log('⏰ Test timeout - closing connection');
  ws.close();
  process.exit(0);
}, 30000); // 30 second timeout
