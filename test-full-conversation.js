#!/usr/bin/env node

import { WebSocket } from 'ws';

const WEBSOCKET_URL = 'ws://localhost:5000/ws';

console.log('🗣️ Testing Full Real-time Conversation...');
console.log('📡 Connecting to:', WEBSOCKET_URL);

const ws = new WebSocket(WEBSOCKET_URL);

let messageCount = 0;
let conversationTurns = 0;
const maxTurns = 3; // Test 3 conversation turns

// Generate realistic speech-like audio data
function generateSpeechAudio(durationMs = 2000, speechIntensity = 0.3) {
  const sampleRate = 24000;
  const samples = Math.floor(sampleRate * durationMs / 1000);
  const audioBuffer = new Int16Array(samples);
  
  // Generate more realistic speech-like patterns
  for (let i = 0; i < samples; i++) {
    // Create speech-like patterns with varying amplitude
    const time = i / sampleRate;
    const frequency = 200 + Math.sin(time * 10) * 50; // Varying frequency
    const amplitude = speechIntensity * (0.5 + 0.5 * Math.sin(time * 3)); // Varying amplitude
    
    // Add some noise and harmonics to make it more speech-like
    const signal = Math.sin(2 * Math.PI * frequency * time) * amplitude;
    const noise = (Math.random() - 0.5) * 0.1 * amplitude;
    const harmonic = Math.sin(2 * Math.PI * frequency * 2 * time) * amplitude * 0.3;
    
    audioBuffer[i] = Math.floor((signal + noise + harmonic) * 16000);
  }
  
  const buffer = Buffer.from(audioBuffer.buffer);
  return buffer.toString('base64');
}

ws.on('open', () => {
  console.log('✅ WebSocket connected');
  
  const startMessage = {
    type: "start",
    userId: "test-user",
    clientId: "test-client",
    useRealtimeAPI: true,
    mode: "realtime",
    behavior: {
      model: "gpt-4o-realtime-preview-2025-06-03",
      temperature: 0.7,
      voice: {
        voice: "shimmer",
        speed: 1.0
      }
    },
    instructions: "You are Vale, an empathetic AI therapeutic assistant. Keep responses very brief (1-2 sentences) for this test. Ask simple questions to keep the conversation flowing."
  };
  
  console.log('📤 Starting conversation...');
  ws.send(JSON.stringify(startMessage));
});

ws.on('message', (data) => {
  messageCount++;
  try {
    const message = JSON.parse(data.toString());
    
    // Only log important events to reduce noise
    if (['ready', 'response.audio_transcript.done', 'response.done', 'conversation.item.input_audio_transcription.completed', 'error'].includes(message.type)) {
      console.log(`📥 ${message.type}`);
    }
    
    switch (message.type) {
      case 'ready':
        console.log('✅ Session ready! Waiting for AI greeting...');
        break;
        
      case 'response.audio_transcript.done':
        console.log(`🤖 AI: "${message.transcript}"`);
        break;
        
      case 'response.done':
        conversationTurns++;
        console.log(`✅ AI response ${conversationTurns} completed`);
        
        if (conversationTurns < maxTurns) {
          // Send user response after a short delay
          setTimeout(() => {
            console.log(`🎤 Sending user response ${conversationTurns}...`);
            
            // Generate speech-like audio
            const audioData = generateSpeechAudio(1500, 0.4); // 1.5 seconds of speech
            
            // Send audio in chunks to simulate real streaming
            const chunkSize = 8000; // 8KB chunks
            const chunks = [];
            for (let i = 0; i < audioData.length; i += chunkSize) {
              chunks.push(audioData.slice(i, i + chunkSize));
            }
            
            console.log(`📤 Sending ${chunks.length} audio chunks...`);
            
            // Send chunks with small delays
            chunks.forEach((chunk, index) => {
              setTimeout(() => {
                ws.send(JSON.stringify({
                  type: 'input_audio_buffer.append',
                  audio: chunk
                }));
                
                // Commit after the last chunk
                if (index === chunks.length - 1) {
                  setTimeout(() => {
                    console.log('📤 Committing audio...');
                    ws.send(JSON.stringify({
                      type: 'input_audio_buffer.commit'
                    }));
                  }, 50);
                }
              }, index * 20); // 20ms between chunks
            });
            
          }, 1000); // Wait 1 second before responding
        } else {
          // End conversation after max turns
          setTimeout(() => {
            console.log('📤 Ending conversation...');
            ws.send(JSON.stringify({
              type: 'conversationEnded'
            }));
          }, 1000);
        }
        break;
        
      case 'conversation.item.input_audio_transcription.completed':
        console.log(`🎯 User speech transcribed: "${message.transcript}"`);
        break;
        
      case 'end_ack':
        console.log('✅ Conversation ended successfully');
        
        setTimeout(() => {
          console.log('\n=== CONVERSATION TEST SUMMARY ===');
          console.log(`✅ Completed ${conversationTurns} conversation turns`);
          console.log(`📊 Total messages processed: ${messageCount}`);
          console.log('🎉 Real-time conversation test PASSED!');
          ws.close();
        }, 500);
        break;
        
      case 'error':
        console.error('❌ Error:', message.message);
        break;
    }
  } catch (error) {
    console.error('❌ Error parsing message:', error);
  }
});

ws.on('close', (code, reason) => {
  console.log(`🔌 Connection closed: ${code} ${reason.toString()}`);
});

ws.on('error', (error) => {
  console.error('❌ WebSocket error:', error);
});

// Timeout to prevent hanging
setTimeout(() => {
  console.log('⏰ Test timeout - conversation test failed');
  ws.close();
  process.exit(1);
}, 60000); // 60 second timeout
